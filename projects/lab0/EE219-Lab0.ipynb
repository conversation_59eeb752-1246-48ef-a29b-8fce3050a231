{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# **Lab 0 PyTorch Tutorial**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Part of the code are referenced from the official PyTorch tutorials [https://pytorch.org/tutorials/beginner/basics/intro.html] and modified by EE219 2024 Fall TA Team. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## **Hello,<PERSON><PERSON>!**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Tensors are a specialized data structure that are very similar to arrays and matrices. In PyTorch, we use tensors to encode the inputs and outputs of a model, as well as the model’s parameters. Tensors can run on GPUs or other hardware accelerators and are optimized for automatic differentiation."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import torch\n", "import numpy as np"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Initializing a Tensor\n", "Tensors can be initialized in various ways. Take a look at the following examples:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["(tensor([[1, 2],\n", "         [3, 4]]),\n", " tensor([[1, 2],\n", "         [3, 4]]))"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["data = [[1, 2],[3, 4]]\n", "\n", "# Directly from data\n", "x_data = torch.tensor(data)\n", "\n", "# From a Numpy array\n", "np_array = np.array(data)\n", "x_np = torch.from_numpy(np_array)\n", "\n", "x_data,x_np"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Tensors can also be initialized with random or constant values. ``shape`` is a tuple of tensor dimensions. In the functions below, it determines the dimensionality of the output tensor."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Random Tensor: \n", " tensor([[0.6527, 0.5421, 0.7493],\n", "        [0.7772, 0.6055, 0.7339]]) \n", "\n", "Ones Tensor: \n", " tensor([[1., 1., 1.],\n", "        [1., 1., 1.]]) \n", "\n", "Zeros Tensor: \n", " tensor([[0., 0., 0.],\n", "        [0., 0., 0.]])\n"]}], "source": ["shape = (2,3,)\n", "rand_tensor = torch.rand(shape)\n", "ones_tensor = torch.ones(shape)\n", "zeros_tensor = torch.zeros(shape)\n", "\n", "print(f\"Random Tensor: \\n {rand_tensor} \\n\")\n", "print(f\"Ones Tensor: \\n {ones_tensor} \\n\")\n", "print(f\"Zeros Tensor: \\n {zeros_tensor}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Attributes of a Tensor\n", "Tensor attributes describe their shape, datatype, and the device on which they are stored."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Shape of tensor: <PERSON>.<PERSON><PERSON>([3, 4])\n", "Datatype of tensor: torch.float32\n", "Device tensor is stored on: cpu\n"]}], "source": ["tensor = torch.rand(3,4)\n", "\n", "print(f\"Shape of tensor: {tensor.shape}\")\n", "print(f\"Datatype of tensor: {tensor.dtype}\")\n", "print(f\"Device tensor is stored on: {tensor.device}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Operations on Tensors\n", "Over 100 tensor operations can be run on the GPU (at typically higher speeds than on a CPU). By default, tensors are created on the CPU. We need to explicitly move tensors to the GPU using ``.to``method. Keep in mind that copying large tensors across devices can be expensive in terms of time and memory!"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Device tensor is stored on: cpu\n"]}], "source": ["# We move our tensor to the GPU if available\n", "if torch.cuda.is_available():\n", "    tensor = tensor.to(\"cuda\")\n", "print(f\"Device tensor is stored on: {tensor.device}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Standard numpy-like indexing and slicing**"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["First row: tensor([1., 1., 1., 1.])\n", "First column: tensor([1., 1., 1., 1.])\n", "Last column: tensor([1., 1., 1., 1.])\n", "tensor([[1., 0., 1., 1.],\n", "        [1., 0., 1., 1.],\n", "        [1., 0., 1., 1.],\n", "        [1., 0., 1., 1.]])\n"]}], "source": ["tensor = torch.ones(4, 4)\n", "print(f\"First row: {tensor[0]}\")\n", "print(f\"First column: {tensor[:, 0]}\")\n", "print(f\"Last column: {tensor[..., -1]}\")\n", "tensor[:,1] = 0\n", "print(tensor)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Joining tensors** \n", "\n", "You can use ``torch.cat`` to concatenate a sequence of tensors along a given dimension."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([[1., 0., 1., 1., 1., 0., 1., 1., 1., 0., 1., 1.],\n", "        [1., 0., 1., 1., 1., 0., 1., 1., 1., 0., 1., 1.],\n", "        [1., 0., 1., 1., 1., 0., 1., 1., 1., 0., 1., 1.],\n", "        [1., 0., 1., 1., 1., 0., 1., 1., 1., 0., 1., 1.]])\n"]}], "source": ["t1 = torch.cat([tensor, tensor, tensor], dim=1)\n", "print(t1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Arithmetic operations**"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[1., 0., 1., 1.],\n", "        [1., 0., 1., 1.],\n", "        [1., 0., 1., 1.],\n", "        [1., 0., 1., 1.]])"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# This computes the matrix multiplication between two tensors. y1, y2, y3 will have the same value\n", "# ``tensor.T`` returns the transpose of a tensor\n", "y1 = tensor @ tensor.T\n", "y2 = tensor.matmul(tensor.T)\n", "\n", "y3 = torch.rand_like(y1)\n", "torch.matmul(tensor, tensor.T, out=y3)\n", "\n", "\n", "# This computes the element-wise product. z1, z2, z3 will have the same value\n", "z1 = tensor * tensor\n", "z2 = tensor.mul(tensor)\n", "\n", "z3 = torch.rand_like(tensor)\n", "torch.mul(tensor, tensor, out=z3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Single-element tensors** \n", "\n", "If you have a one-element tensor, for example by aggregating all values of a tensor into one value, you can convert it to a Python numerical value using ``item()``:"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["12.0 <class 'float'>\n"]}], "source": ["agg = tensor.sum()\n", "agg_item = agg.item()\n", "print(agg_item, type(agg_item))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**In-place operations** \n", "\n", "Operations that store the result into the operand are called in-place. They are denoted by a `_` suffix. \n", "\n", "In-place operations save some memory, but can be problematic when computing derivatives because of an immediate loss of history. Hence, their use is discouraged."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([[1., 0., 1., 1.],\n", "        [1., 0., 1., 1.],\n", "        [1., 0., 1., 1.],\n", "        [1., 0., 1., 1.]]) \n", "\n", "tensor([[6., 5., 6., 6.],\n", "        [6., 5., 6., 6.],\n", "        [6., 5., 6., 6.],\n", "        [6., 5., 6., 6.]])\n"]}], "source": ["print(f\"{tensor} \\n\")\n", "tensor.add_(5)\n", "print(tensor)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Tensor to NumPy array"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["t: tensor([1., 1., 1., 1., 1.])\n", "n: [1. 1. 1. 1. 1.]\n"]}], "source": ["t = torch.ones(5)\n", "print(f\"t: {t}\")\n", "n = t.numpy()\n", "print(f\"n: {n}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### NumPy array to Tensor ###"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["n = np.ones(5)\n", "t = torch.from_numpy(n)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## **Datasets & DataLoaders**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Loading a Dataset\n", "Here is an example of how to load the Fashion-MNIST dataset from TorchVision. Fashion-MNIST is a dataset of <PERSON><PERSON><PERSON>’s article images consisting of 60,000 training examples and 10,000 test examples. Each example comprises a 28×28 grayscale image and an associated label from one of 10 classes.\n", "\n", "We load the FashionMNIST Dataset with the following parameters:\n", "\n", "1. `root` is the path where the train/test data is stored,\n", "\n", "2. `train specifies` training or test dataset,\n", "\n", "3. `download=True` downloads the data from the internet if it’s not available at root.\n", "\n", "4. `transform` and `target_transform` specify the feature and label transformations"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 26.4M/26.4M [01:04<00:00, 411kB/s] \n", "100%|██████████| 29.5k/29.5k [00:00<00:00, 69.9kB/s]\n", "100%|██████████| 4.42M/4.42M [00:03<00:00, 1.35MB/s]\n", "100%|██████████| 5.15k/5.15k [00:00<00:00, 14.5MB/s]\n"]}], "source": ["import torch\n", "from torch.utils.data import Dataset\n", "from torchvision import datasets\n", "from torchvision.transforms import ToTensor\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "training_data = datasets.FashionMNIST(\n", "    root=\"../data\",\n", "    train=True,\n", "    download=True,\n", "    transform=ToTensor()\n", ")\n", "\n", "test_data = datasets.FashionMNIST(\n", "    root=\"../data\",\n", "    train=False,\n", "    download=True,\n", "    transform=ToTensor()\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Iterating and Visualizing the Dataset\n", "We can index Datasets manually like a list: `training_data[index]`. We use matplotlib to visualize some samples in our training data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x800 with 9 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["labels_map = {\n", "    0: \"T-Shirt\",\n", "    1: \"Trouser\",\n", "    2: \"Pullover\",\n", "    3: \"Dress\",\n", "    4: \"Coat\",\n", "    5: \"<PERSON><PERSON>\",\n", "    6: \"Shirt\",\n", "    7: \"Sneaker\",\n", "    8: \"Bag\",\n", "    9: \"<PERSON>kle Boot\",\n", "}\n", "figure = plt.figure(figsize=(8, 8))\n", "cols, rows = 3, 3\n", "for i in range(1, cols * rows + 1):\n", "    sample_idx = torch.randint(len(training_data), size=(1,)).item()\n", "    img, label = training_data[sample_idx]\n", "    figure.add_subplot(rows, cols, i)\n", "    plt.title(labels_map[int(label)])\n", "    plt.axis(\"off\")\n", "    plt.imshow(img.squeeze(), cmap=\"gray\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Preparing your data for training with DataLoaders\n", "The `Dataset` retrieves our dataset’s features and labels one sample at a time. While training a model, we typically want to pass samples in “minibatches”, reshuffle the data at every epoch to reduce model overfitting, and use Python’s 'multiprocessing' to speed up data retrieval.\n", "\n", "`DataLoader` is an iterable that abstracts this complexity for us in an easy API."]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["from torch.utils.data import DataLoader\n", "\n", "train_dataloader = DataLoader(training_data, batch_size=64, shuffle=True)\n", "test_dataloader = DataLoader(test_data, batch_size=64, shuffle=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Iterate through the DataLoader\n", "We have loaded that dataset into the `DataLoader` and can iterate through the dataset as needed. Each iteration below returns a batch of train_features and train_labels (containing batch_size=64 features and labels respectively). Because we specified `shuffle=True`, after we iterate over all batches the data is shuffled (for finer-grained control over the data loading order, take a look at Samplers)."]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Feature batch shape: torch.Size([64, 1, 28, 28])\n", "Labels batch shape: torch.Size([64])\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Label: 6\n"]}], "source": ["# Display image and label.\n", "train_features, train_labels = next(iter(train_dataloader))\n", "print(f\"Feature batch shape: {train_features.size()}\")\n", "print(f\"Labels batch shape: {train_labels.size()}\")\n", "img = train_features[0].squeeze()\n", "label = train_labels[0]\n", "plt.imshow(img, cmap=\"gray\")\n", "plt.show()\n", "print(f\"Label: {label}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Transforms\n", "Data does not always come in its final processed form that is required for training machine learning algorithms. We use transforms to perform some manipulation of the data and make it suitable for training.\n", "\n", "All TorchVision datasets have two parameters `-transform` to modify the features and `target_transform` to modify the labels - that accept callables containing the transformation logic. The `torchvision.transforms` module offers several commonly-used transforms out of the box.\n", "\n", "The FashionMNIST features are in PIL Image format, and the labels are integers. For training, we need the features as normalized tensors, and the labels as one-hot encoded tensors. To make these transformations, we use `ToTensor` and `Lambda`."]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "`ToTensor` converts a PIL image or NumPy ndarray into a `FloatTensor`. and scales the image’s pixel intensity values in the range [0., 1.]\n", "\n", "`Lambda` transforms apply any user-defined lambda function. Here, we define a function to turn the integer into a one-hot encoded tensor. It first creates a zero tensor of size 10 (the number of labels in our dataset) and calls `scatter_` which assigns a value=1 on the index as given by the label y."]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["import torch\n", "from torchvision import datasets\n", "from torchvision.transforms import ToTensor, Lambda\n", "\n", "ds = datasets.FashionMNIST(\n", "    root=\"../data\",\n", "    train=True,\n", "    download=True,\n", "    transform=ToTensor(),\n", "    target_transform=Lambda(lambda y: torch.zeros(10, dtype=torch.float).scatter_(0, torch.tensor(y), value=1))\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## **Build A Simple Neural Network**\n", "Neural networks comprise of layers/modules that perform operations on data. The `torch.nn` namespace provides all the building blocks you need to build your own neural network. Every module in PyTorch subclasses the `nn.Module`. A neural network is a module itself that consists of other modules (layers). This nested structure allows for building and managing complex architectures easily.\n", "\n", "In the following sections, we’ll build a neural network to classify images in the FashionMNIST dataset."]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["import os\n", "import torch\n", "from torch import nn\n", "from torch.utils.data import DataLoader\n", "from torchvision import datasets, transforms"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Get Devi<PERSON> for Training\n", "We want to be able to train our model on a hardware accelerator like the GPU, if available. Let’s check to see if `torch.cuda` is available, otherwise we use the CPU."]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using cpu device\n"]}], "source": ["device = (\n", "    \"cuda\"\n", "    if torch.cuda.is_available()\n", "    else \"cpu\"\n", ")\n", "print(f\"Using {device} device\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Define the Class\n", "We define our neural network by subclassing `nn.Module`, and initialize the neural network layers in `__init__`. Every `nn.Module` subclass implements the operations on input data in the forward method."]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["class NeuralNetwork(nn.Module):\n", "    def __init__(self):\n", "        super().__init__()\n", "        self.flatten = nn.Flatten()\n", "        self.linear_relu_stack = nn.Sequential(\n", "            nn.<PERSON><PERSON>(28*28, 512),\n", "            nn.ReLU(),\n", "            nn.<PERSON><PERSON>(512, 512),\n", "            nn.ReLU(),\n", "            nn.<PERSON><PERSON>(512, 10),\n", "        )\n", "\n", "    def forward(self, x):\n", "        x = self.flatten(x)\n", "        logits = self.linear_relu_stack(x)\n", "        return logits"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We create an instance of NeuralNetwork, and move it to the device, and print its structure."]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NeuralNetwork(\n", "  (flatten): Flatten(start_dim=1, end_dim=-1)\n", "  (linear_relu_stack): Sequential(\n", "    (0): Linear(in_features=784, out_features=512, bias=True)\n", "    (1): ReLU()\n", "    (2): Linear(in_features=512, out_features=512, bias=True)\n", "    (3): ReLU()\n", "    (4): Linear(in_features=512, out_features=10, bias=True)\n", "  )\n", ")\n"]}], "source": ["model = NeuralNetwork().to(device)\n", "print(model)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To use the model, we pass it the input data. This executes the model’s forward, along with some background operations. \n", "\n", "Calling the model on the input returns a 2-dimensional tensor with dim=0 corresponding to each output of 10 raw predicted values for each class, and dim=1 corresponding to the individual values of each output. We get the prediction probabilities by passing it through an instance of the `nn.Softmax` module."]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Predicted class: tensor([8])\n"]}], "source": ["X = torch.rand(1, 28, 28, device=device)\n", "logits = model(X)\n", "pred_probab = nn.Softmax(dim=1)(logits)\n", "y_pred = pred_probab.argmax(1)\n", "print(f\"Predicted class: {y_pred}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Model Layers\n", "Let’s break down the layers in the FashionMNIST model. To illustrate it, we will take a sample minibatch of 3 images of size 28x28 and see what happens to it as we pass it through the network."]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON>.<PERSON><PERSON>([3, 28, 28])\n"]}], "source": ["input_image = torch.rand(3,28,28)\n", "print(input_image.size())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### nn.<PERSON><PERSON>\n", "We initialize the `nn.Flatten` layer to convert each 2D 28x28 image into a contiguous array of 784 pixel values ( the minibatch dimension (at dim=0) is maintained)."]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([3, 784])\n"]}], "source": ["flatten = nn.Flatten()\n", "flat_image = flatten(input_image)\n", "print(flat_image.size())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### nn.Linear\n", "The linear layer is a module that applies a linear transformation on the input using its stored weights and biases."]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.<PERSON><PERSON>([3, 20])\n"]}], "source": ["layer1 = nn.Linear(in_features=28*28, out_features=20)\n", "hidden1 = layer1(flat_image)\n", "print(hidden1.size())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### nn.ReLU\n", "Non-linear activations are what create the complex mappings between the model’s inputs and outputs. They are applied after linear transformations to introduce nonlinearity, helping neural networks learn a wide variety of phenomena.\n", "\n", "In this model, we use `nn.ReLU` between our linear layers, but there’s other activations to introduce non-linearity in your model."]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Before ReLU: tensor([[ 0.2657,  0.0111,  0.0458,  0.3051, -0.6632, -0.3624,  0.3881, -0.2013,\n", "          0.3991, -0.3806,  0.1132,  0.1245, -0.2520,  0.1732, -0.2481, -0.2940,\n", "         -0.2012,  0.4381, -0.5697, -0.2993],\n", "        [ 0.0281, -0.1819, -0.1445,  0.3254, -0.6590,  0.0060,  0.1279, -0.3132,\n", "          0.5455,  0.0158,  0.0281,  0.0429, -0.1075,  0.4675,  0.1132, -0.2555,\n", "         -0.5250,  0.2819, -0.5423, -0.3072],\n", "        [ 0.1973,  0.1628,  0.0084,  0.4651, -0.6281, -0.0107,  0.2476, -0.2838,\n", "         -0.1568, -0.4604,  0.3955, -0.0770,  0.0298,  0.5430, -0.0827, -0.5070,\n", "         -0.2539,  0.2406, -0.3948, -0.3916]], grad_fn=<AddmmBackward0>)\n", "\n", "\n", "After ReLU: tensor([[0.2657, 0.0111, 0.0458, 0.3051, 0.0000, 0.0000, 0.3881, 0.0000, 0.3991,\n", "         0.0000, 0.1132, 0.1245, 0.0000, 0.1732, 0.0000, 0.0000, 0.0000, 0.4381,\n", "         0.0000, 0.0000],\n", "        [0.0281, 0.0000, 0.0000, 0.3254, 0.0000, 0.0060, 0.1279, 0.0000, 0.5455,\n", "         0.0158, 0.0281, 0.0429, 0.0000, 0.4675, 0.1132, 0.0000, 0.0000, 0.2819,\n", "         0.0000, 0.0000],\n", "        [0.1973, 0.1628, 0.0084, 0.4651, 0.0000, 0.0000, 0.2476, 0.0000, 0.0000,\n", "         0.0000, 0.3955, 0.0000, 0.0298, 0.5430, 0.0000, 0.0000, 0.0000, 0.2406,\n", "         0.0000, 0.0000]], grad_fn=<ReluBackward0>)\n"]}], "source": ["print(f\"Before ReLU: {hidden1}\\n\\n\")\n", "hidden1 = nn.ReLU()(hidden1)\n", "print(f\"After ReLU: {hidden1}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### nn.Sequential\n", "`nn.Sequential` is an ordered container of modules. The data is passed through all the modules in the same order as defined. "]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["seq_modules = nn.Sequential(\n", "    flatten,\n", "    layer1,\n", "    nn.ReLU(),\n", "    nn.<PERSON><PERSON>(20, 10)\n", ")\n", "input_image = torch.rand(3,28,28)\n", "logits = seq_modules(input_image)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### nn.Softmax\n", "The last linear layer of the neural network returns logits - raw values in [-infty, infty] - which are passed to the `nn.Softmax` module. The logits are scaled to values [0, 1] representing the model’s predicted probabilities for each class. `dim` parameter indicates the dimension along which the values must sum to 1."]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["softmax = nn.Softmax(dim=1)\n", "pred_probab = softmax(logits)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Model Parameters\n", "Many layers inside a neural network have associated weights and biases that are optimized during training. Subclassing nn.Module automatically tracks all fields defined inside your model object, and makes all parameters accessible using your model’s `parameters()` or `named_parameters()` methods.\n", "\n", "In this example, we iterate over each parameter, and print its size and a preview of its values."]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model structure: NeuralNetwork(\n", "  (flatten): Flatten(start_dim=1, end_dim=-1)\n", "  (linear_relu_stack): Sequential(\n", "    (0): Linear(in_features=784, out_features=512, bias=True)\n", "    (1): ReLU()\n", "    (2): Linear(in_features=512, out_features=512, bias=True)\n", "    (3): ReLU()\n", "    (4): Linear(in_features=512, out_features=10, bias=True)\n", "  )\n", ")\n", "\n", "\n", "Layer: linear_relu_stack.0.weight | Size: torch.Size([512, 784]) | Values : tensor([[ 0.0255, -0.0104,  0.0182,  ..., -0.0160, -0.0239, -0.0208],\n", "        [-0.0009, -0.0115,  0.0009,  ..., -0.0019, -0.0347,  0.0125]],\n", "       grad_fn=<SliceBackward0>) \n", "\n", "Layer: linear_relu_stack.0.bias | Size: torch.Size([512]) | Values : tensor([ 0.0280, -0.0069], grad_fn=<SliceBackward0>) \n", "\n", "Layer: linear_relu_stack.2.weight | Size: torch.Size([512, 512]) | Values : tensor([[ 0.0015,  0.0008, -0.0339,  ..., -0.0098,  0.0322, -0.0026],\n", "        [ 0.0297,  0.0094, -0.0021,  ..., -0.0026,  0.0183,  0.0224]],\n", "       grad_fn=<SliceBackward0>) \n", "\n", "Layer: linear_relu_stack.2.bias | Size: torch.Size([512]) | Values : tensor([0.0409, 0.0433], grad_fn=<SliceBackward0>) \n", "\n", "Layer: linear_relu_stack.4.weight | Size: torch.Size([10, 512]) | Values : tensor([[ 0.0361, -0.0336,  0.0339,  ..., -0.0243, -0.0224, -0.0407],\n", "        [-0.0316, -0.0195,  0.0045,  ..., -0.0201, -0.0371,  0.0231]],\n", "       grad_fn=<SliceBackward0>) \n", "\n", "Layer: linear_relu_stack.4.bias | Size: torch.Size([10]) | Values : tensor([-0.0280, -0.0361], grad_fn=<SliceBackward0>) \n", "\n"]}], "source": ["print(f\"Model structure: {model}\\n\\n\")\n", "\n", "for name, param in model.named_parameters():\n", "    print(f\"Layer: {name} | Size: {param.size()} | Values : {param[:2]} \\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## **Automatic Differentiation with torch.autograd**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["When training neural networks, the most frequently used algorithm is back propagation. In this algorithm, parameters (model weights) are adjusted according to the gradient of the loss function with respect to the given parameter.\n", "\n", "To compute those gradients, PyTorch has a built-in differentiation engine called `torch.autograd`. It supports automatic computation of gradient for any computational graph.\n", "\n", "Consider the simplest one-layer neural network, with input x, parameters w and b, and some loss function. It can be defined in PyTorch in the following manner:"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["import torch\n", "\n", "x = torch.ones(5)  # input tensor\n", "y = torch.zeros(3)  # expected output\n", "w = torch.randn(5, 3, requires_grad=True)\n", "b = torch.randn(3, requires_grad=True)\n", "z = torch.matmul(x, w)+b\n", "loss = torch.nn.functional.binary_cross_entropy_with_logits(z, y)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Tensors, Functions and Computational graph"]}, {"attachments": {"image-2.png": {"image/png": "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"}}, "cell_type": "markdown", "metadata": {}, "source": ["This code defines the following computational graph:\n", "\n", "![image-2.png](attachment:image-2.png)\n", "\n", "In this network, `w` and `b` are parameters, which we need to optimize. Thus, we need to be able to compute the gradients of loss function with respect to those variables. In order to do that, we set the `requires_grad` property of those tensors.\n", "\n", "You can set the value of `requires_grad` when creating a tensor, or later by using `x.requires_grad_(True)` method.\n", "\n", "A function that we apply to tensors to construct computational graph is in fact an object of class Function. This object knows how to compute the function in the forward direction, and also how to compute its derivative during the backward propagation step. A reference to the backward propagation function is stored in grad_fn property of a tensor. You can find more information of Function in the documentation."]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Gradient function for z = <AddBackward0 object at 0x7152fd8aae30>\n", "Gradient function for loss = <BinaryCrossEntropyWithLogitsBackward0 object at 0x7152f887df60>\n"]}], "source": ["print(f\"Gradient function for z = {z.grad_fn}\")\n", "print(f\"Gradient function for loss = {loss.grad_fn}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Computing Gradients\n", "To optimize weights of parameters in the neural network, we need to compute the derivatives of our loss function with respect to parameters. To compute those derivatives, we call `loss.backward()`, and then retrieve the values from `w.grad` and `b.grad`:"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([[0.0084, 0.0006, 0.2174],\n", "        [0.0084, 0.0006, 0.2174],\n", "        [0.0084, 0.0006, 0.2174],\n", "        [0.0084, 0.0006, 0.2174],\n", "        [0.0084, 0.0006, 0.2174]])\n", "tensor([0.0084, 0.0006, 0.2174])\n"]}], "source": ["loss.backward()\n", "print(w.grad)\n", "print(b.grad)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can only obtain the grad properties for the leaf nodes of the computational graph, which have `requires_grad` property set to True. For all other nodes in our graph, gradients will not be available.\n", "\n", "We can only perform gradient calculations using backward once on a given graph, for performance reasons. If we need to do several backward calls on the same graph, we need to pass `retain_graph=True` to the backward call."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Disabling Gradient Tracking\n", "By default, all tensors with `requires_grad=True` are tracking their computational history and support gradient computation. However, there are some cases when we do not need to do that, for example, when we have trained the model and just want to apply it to some input data, i.e. we only want to do forward computations through the network. We can stop tracking computations by surrounding our computation code with `torch.no_grad()` block:"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n", "False\n"]}], "source": ["z = torch.matmul(x, w)+b\n", "print(z.requires_grad)\n", "\n", "with torch.no_grad():\n", "    z = torch.matmul(x, w)+b\n", "print(z.requires_grad)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Another way to achieve the same result is to use the `detach()` method on the tensor:"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n"]}], "source": ["z = torch.matmul(x, w)+b\n", "z_det = z.detach()\n", "print(z_det.requires_grad)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There are reasons you might want to disable gradient tracking:\n", "To mark some parameters in your neural network as frozen parameters.\n", "\n", "To speed up computations when you are only doing forward pass, because computations on tensors that do not track gradients would be more efficient."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## **Optimizing Model Parameters**\n", "Now that we have a model and data, it’s time to train, validate and test our model by optimizing its parameters on our data. \n", "\n", "Training a model is an iterative process; in each iteration the model makes a guess about the output, calculates the error in its guess (loss), collects the derivatives of the error with respect to its parameters (as we saw in the previous section), and optimizes these parameters using gradient descent. "]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "from torch.utils.data import DataLoader\n", "from torchvision import datasets\n", "from torchvision.transforms import ToTensor\n", "\n", "training_data = datasets.FashionMNIST(\n", "    root=\"../data\",\n", "    train=True,\n", "    download=True,\n", "    transform=ToTensor()\n", ")\n", "\n", "test_data = datasets.FashionMNIST(\n", "    root=\"../data\",\n", "    train=False,\n", "    download=True,\n", "    transform=ToTensor()\n", ")\n", "\n", "train_dataloader = DataLoader(training_data, batch_size=64)\n", "test_dataloader = DataLoader(test_data, batch_size=64)\n", "\n", "class NeuralNetwork(nn.Module):\n", "    def __init__(self):\n", "        super().__init__()\n", "        self.flatten = nn.Flatten()\n", "        self.linear_relu_stack = nn.Sequential(\n", "            nn.<PERSON><PERSON>(28*28, 512),\n", "            nn.ReLU(),\n", "            nn.<PERSON><PERSON>(512, 512),\n", "            nn.ReLU(),\n", "            nn.<PERSON><PERSON>(512, 10),\n", "        )\n", "\n", "    def forward(self, x):\n", "        x = self.flatten(x)\n", "        logits = self.linear_relu_stack(x)\n", "        return logits\n", "\n", "model = NeuralNetwork()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Hyperparameters\n", "Hyperparameters are adjustable parameters that let you control the model optimization process. Different hyperparameter values can impact model training and convergence rates (read more about hyperparameter tuning)\n", "\n", "We define the following hyperparameters for training:\n", "\n", "1. Number of Epochs - the number times to iterate over the dataset\n", "\n", "2. <PERSON><PERSON> - the number of data samples propagated through the network before the parameters are updated\n", "\n", "3. Learning Rate - how much to update models parameters at each batch/epoch. Smaller values yield slow learning speed, while large values may result in unpredictable behavior during training."]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["learning_rate = 1e-3\n", "batch_size = 64\n", "epochs = 5"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Optimization Loop\n", "Once we set our hyperparameters, we can then train and optimize our model with an optimization loop. Each iteration of the optimization loop is called an `epoch`.\n", "\n", "Each epoch consists of two main parts:\n", "\n", "1. The Train Loop - iterate over the training dataset and try to converge to optimal parameters.\n", "\n", "2. The Validation/Test Loop - iterate over the test dataset to check if model performance is improving."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Loss Function\n", "When presented with some training data, our untrained network is likely not to give the correct answer. Loss function measures the degree of dissimilarity of obtained result to the target value, and it is the loss function that we want to minimize during training. \n", "\n", "To calculate the loss we make a prediction using the inputs of our given data sample and compare it against the true data label value.\n", "\n", "Common loss functions include `nn.MSELoss` (Mean Square Error) for regression tasks, and `nn.NLLLoss` (Negative Log Likelihood) for classification. `nn.CrossEntropyLoss` combines `nn.LogSoftmax` and `nn.NLLLoss`. We pass our model’s output logits to `nn.CrossEntropyLoss`, which will normalize the logits and compute the prediction error."]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["# Initialize the loss function\n", "loss_fn = nn.CrossEntropyLoss()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Optimizer\n", "Optimization is the process of adjusting model parameters to reduce model error in each training step. Optimization algorithms define how this process is performed (in this example we use Stochastic Gradient Descent). All optimization logic is encapsulated in the optimizer object. Here, we use the `SGD` optimizer; additionally, there are many different optimizers available in PyTorch such as `ADAM` and `RMSProp`, that work better for different kinds of models and data.\n", "\n", "We initialize the optimizer by registering the model’s parameters that need to be trained, and passing in the learning rate hyperparameter."]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["optimizer = torch.optim.SGD(model.parameters(), lr=learning_rate)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Inside the training loop, optimization happens in three steps:\n", "\n", "1. Call `optimizer.zero_grad() `to reset the gradients of model parameters. Gradients by default add up; to prevent double-counting, we explicitly zero them at each iteration.\n", "\n", "2. Backpropagate the prediction loss with a call to `loss.backward()`. PyTorch deposits the gradients of the loss w.r.t. each parameter.\n", "\n", "3. Once we have our gradients, we call `optimizer.step()` to adjust the parameters by the gradients collected in the backward pass."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Full Implementation\n", "We define `train_loop` that loops over our optimization code, and `test_loop` that evaluates the model’s performance against our test data."]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["def train_loop(dataloader, model, loss_fn, optimizer):\n", "    size = len(dataloader.dataset)\n", "    # Set the model to training mode - important for batch normalization and dropout layers\n", "    # Unnecessary in this situation but added for best practices\n", "    model.train()\n", "    for batch, (X, y) in enumerate(dataloader):\n", "        # Compute prediction and loss\n", "        pred = model(X)\n", "        loss = loss_fn(pred, y)\n", "\n", "        # Backpropagation\n", "        loss.backward()\n", "        optimizer.step()\n", "        optimizer.zero_grad()\n", "\n", "        if batch % 100 == 0:\n", "            loss, current = loss.item(), batch * batch_size + len(X)\n", "            print(f\"loss: {loss:>7f}  [{current:>5d}/{size:>5d}]\")\n", "\n", "\n", "def test_loop(dataloader, model, loss_fn):\n", "    # Set the model to evaluation mode - important for batch normalization and dropout layers\n", "    # Unnecessary in this situation but added for best practices\n", "    model.eval()\n", "    size = len(dataloader.dataset)\n", "    num_batches = len(dataloader)\n", "    test_loss, correct = 0, 0\n", "\n", "    # Evaluating the model with torch.no_grad() ensures that no gradients are computed during test mode\n", "    # also serves to reduce unnecessary gradient computations and memory usage for tensors with requires_grad=True\n", "    with torch.no_grad():\n", "        for X, y in dataloader:\n", "            pred = model(X)\n", "            test_loss += loss_fn(pred, y).item()\n", "            correct += (pred.argmax(1) == y).type(torch.float).sum().item()\n", "\n", "    test_loss /= num_batches\n", "    correct /= size\n", "    print(f\"Test Error: \\n Accuracy: {(100*correct):>0.1f}%, Avg loss: {test_loss:>8f} \\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We initialize the loss function and optimizer, and pass it to `train_loop` and `test_loop`. Feel free to increase the number of epochs to track the model’s improving performance."]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1\n", "-------------------------------\n", "loss: 2.303587  [   64/60000]\n", "loss: 2.285153  [ 6464/60000]\n", "loss: 2.264740  [12864/60000]\n", "loss: 2.260786  [19264/60000]\n", "loss: 2.247761  [25664/60000]\n", "loss: 2.212105  [32064/60000]\n", "loss: 2.214254  [38464/60000]\n", "loss: 2.170552  [44864/60000]\n", "loss: 2.167662  [51264/60000]\n", "loss: 2.132989  [57664/60000]\n", "Test Error: \n", " Accuracy: 48.8%, Avg loss: 2.121109 \n", "\n", "Epoch 2\n", "-------------------------------\n", "loss: 2.135917  [   64/60000]\n", "loss: 2.113027  [ 6464/60000]\n", "loss: 2.052626  [12864/60000]\n", "loss: 2.070507  [19264/60000]\n", "loss: 2.007812  [25664/60000]\n", "loss: 1.951149  [32064/60000]\n", "loss: 1.973886  [38464/60000]\n", "loss: 1.880110  [44864/60000]\n", "loss: 1.894600  [51264/60000]\n", "loss: 1.816491  [57664/60000]\n", "Test Error: \n", " Accuracy: 51.9%, Avg loss: 1.809173 \n", "\n", "Epoch 3\n", "-------------------------------\n", "loss: 1.855401  [   64/60000]\n", "loss: 1.806902  [ 6464/60000]\n", "loss: 1.692052  [12864/60000]\n", "loss: 1.741359  [19264/60000]\n", "loss: 1.621959  [25664/60000]\n", "loss: 1.590734  [32064/60000]\n", "loss: 1.612285  [38464/60000]\n", "loss: 1.505908  [44864/60000]\n", "loss: 1.546136  [51264/60000]\n", "loss: 1.441855  [57664/60000]\n", "Test Error: \n", " Accuracy: 60.6%, Avg loss: 1.455217 \n", "\n", "Epoch 4\n", "-------------------------------\n", "loss: 1.529272  [   64/60000]\n", "loss: 1.484155  [ 6464/60000]\n", "loss: 1.340674  [12864/60000]\n", "loss: 1.425758  [19264/60000]\n", "loss: 1.303592  [25664/60000]\n", "loss: 1.310417  [32064/60000]\n", "loss: 1.326294  [38464/60000]\n", "loss: 1.243587  [44864/60000]\n", "loss: 1.286428  [51264/60000]\n", "loss: 1.193278  [57664/60000]\n", "Test Error: \n", " Accuracy: 63.6%, Avg loss: 1.214727 \n", "\n", "Epoch 5\n", "-------------------------------\n", "loss: 1.289814  [   64/60000]\n", "loss: 1.265828  [ 6464/60000]\n", "loss: 1.106761  [12864/60000]\n", "loss: 1.223788  [19264/60000]\n", "loss: 1.098788  [25664/60000]\n", "loss: 1.129756  [32064/60000]\n", "loss: 1.150839  [38464/60000]\n", "loss: 1.079911  [44864/60000]\n", "loss: 1.121948  [51264/60000]\n", "loss: 1.044896  [57664/60000]\n", "Test Error: \n", " Accuracy: 65.0%, Avg loss: 1.063605 \n", "\n", "Epoch 6\n", "-------------------------------\n", "loss: 1.128437  [   64/60000]\n", "loss: 1.126803  [ 6464/60000]\n", "loss: 0.951682  [12864/60000]\n", "loss: 1.093900  [19264/60000]\n", "loss: 0.971327  [25664/60000]\n", "loss: 1.005949  [32064/60000]\n", "loss: 1.039993  [38464/60000]\n", "loss: 0.975054  [44864/60000]\n", "loss: 1.012393  [51264/60000]\n", "loss: 0.950928  [57664/60000]\n", "Test Error: \n", " Accuracy: 65.8%, Avg loss: 0.964829 \n", "\n", "Epoch 7\n", "-------------------------------\n", "loss: 1.015440  [   64/60000]\n", "loss: 1.035973  [ 6464/60000]\n", "loss: 0.845229  [12864/60000]\n", "loss: 1.006276  [19264/60000]\n", "loss: 0.890014  [25664/60000]\n", "loss: 0.918168  [32064/60000]\n", "loss: 0.966351  [38464/60000]\n", "loss: 0.907075  [44864/60000]\n", "loss: 0.936183  [51264/60000]\n", "loss: 0.888087  [57664/60000]\n", "Test Error: \n", " Accuracy: 67.0%, Avg loss: 0.897196 \n", "\n", "Epoch 8\n", "-------------------------------\n", "loss: 0.932695  [   64/60000]\n", "loss: 0.972688  [ 6464/60000]\n", "loss: 0.768641  [12864/60000]\n", "loss: 0.944610  [19264/60000]\n", "loss: 0.834804  [25664/60000]\n", "loss: 0.854228  [32064/60000]\n", "loss: 0.914420  [38464/60000]\n", "loss: 0.861985  [44864/60000]\n", "loss: 0.881528  [51264/60000]\n", "loss: 0.842937  [57664/60000]\n", "Test Error: \n", " Accuracy: 68.4%, Avg loss: 0.848486 \n", "\n", "Epoch 9\n", "-------------------------------\n", "loss: 0.869335  [   64/60000]\n", "loss: 0.925472  [ 6464/60000]\n", "loss: 0.711257  [12864/60000]\n", "loss: 0.899364  [19264/60000]\n", "loss: 0.794831  [25664/60000]\n", "loss: 0.806155  [32064/60000]\n", "loss: 0.875182  [38464/60000]\n", "loss: 0.830558  [44864/60000]\n", "loss: 0.840927  [51264/60000]\n", "loss: 0.808325  [57664/60000]\n", "Test Error: \n", " Accuracy: 69.6%, Avg loss: 0.811426 \n", "\n", "Epoch 10\n", "-------------------------------\n", "loss: 0.818783  [   64/60000]\n", "loss: 0.887616  [ 6464/60000]\n", "loss: 0.666298  [12864/60000]\n", "loss: 0.864639  [19264/60000]\n", "loss: 0.763499  [25664/60000]\n", "loss: 0.769114  [32064/60000]\n", "loss: 0.843651  [38464/60000]\n", "loss: 0.807313  [44864/60000]\n", "loss: 0.809626  [51264/60000]\n", "loss: 0.780509  [57664/60000]\n", "Test Error: \n", " Accuracy: 70.9%, Avg loss: 0.781817 \n", "\n", "Done!\n"]}], "source": ["loss_fn = nn.CrossEntropyLoss()\n", "optimizer = torch.optim.SGD(model.parameters(), lr=learning_rate)\n", "\n", "epochs = 10\n", "for t in range(epochs):\n", "    print(f\"Epoch {t+1}\\n-------------------------------\")\n", "    train_loop(train_dataloader, model, loss_fn, optimizer)\n", "    test_loop(test_dataloader, model, loss_fn)\n", "print(\"Done!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Visualization\n", "We can visualize the model's prediction to see how the model truly performs."]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 2000x1000 with 40 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["labels_map = {\n", "    0: \"T-Shirt\",\n", "    1: \"Trouser\",\n", "    2: \"Pullover\",\n", "    3: \"Dress\",\n", "    4: \"Coat\",\n", "    5: \"<PERSON><PERSON>\",\n", "    6: \"Shirt\",\n", "    7: \"Sneaker\",\n", "    8: \"Bag\",\n", "    9: \"<PERSON>kle Boot\",\n", "}\n", "plt.figure(figsize=(20, 10))\n", "\n", "test_sample ={}\n", "for index,batch in enumerate(test_dataloader):\n", "    if index == 1:\n", "        break \n", "    else:\n", "        test_sample[index] = batch\n", "\n", "for index in range(40):\n", "  image, label = test_sample[0][0][index],test_sample[0][1][index]\n", "\n", "  # Model inference\n", "  model.eval()\n", "  with torch.no_grad():\n", "    pred = model(image)\n", "    pred = pred.argmax(dim=1)\n", "    \n", "  # Convert from CHW to HWC for visualization\n", "  image = image.permute(1, 2, 0)\n", "\n", "  # Convert from class indices to class names\n", "  pred = labels_map[pred.item()]\n", "  label = labels_map[label.item()]\n", "\n", "  # Visualize the image\n", "  plt.subplot(4, 10, index + 1)\n", "  plt.imshow(image,cmap=\"gray\")\n", "  plt.title(f\"pred: {pred}\" + \"\\n\" + f\"label: {label}\")\n", "  plt.axis(\"off\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## **Save and Load the Model**\n", "In this section we will look at how to persist model state with saving, loading and running model predictions."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Saving and Loading Model Weights\n", "PyTorch models store the learned parameters in an internal state dictionary, called `state_dict`. These can be persisted via the `torch.save` method:"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [], "source": ["torch.save(model.state_dict(), 'model_weights.pth')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To load model weights, you need to create an instance of the same model first, and then load the parameters using `load_state_dict()` method.\n", "\n", "In the code below, we set `weights_only=True` to limit the functions executed during unpickling to only those necessary for loading weights. Using `weights_only=True` is considered a best practice when loading weights."]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"data": {"text/plain": ["NeuralNetwork(\n", "  (flatten): Flatten(start_dim=1, end_dim=-1)\n", "  (linear_relu_stack): Sequential(\n", "    (0): Linear(in_features=784, out_features=512, bias=True)\n", "    (1): ReLU()\n", "    (2): Linear(in_features=512, out_features=512, bias=True)\n", "    (3): ReLU()\n", "    (4): Linear(in_features=512, out_features=10, bias=True)\n", "  )\n", ")"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["model = NeuralNetwork()\n", "model.load_state_dict(torch.load('model_weights.pth', weights_only=True))\n", "model.eval()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Be sure to call `model.eval()` method before inferencing to set the dropout and batch normalization layers to evaluation mode. Failing to do this will yield inconsistent inference results."]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 2}