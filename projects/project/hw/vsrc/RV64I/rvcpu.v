
`timescale 1ns / 1ps

`define z16APu5lI  64'h00000000_00000000
`define hJZJwMU5   64'h00000000_80000000   
`define TkzResQf   8'h11

`define kjrX6VMlvI      32
`define ObcjYsbppT7SA   31 : 0
`define VJPNWZIH6zdse   63 : 0 

`define JUPQDrkYC       64
`define muFKlpd         63 : 0
`define bDsIxaO3BVSK    4  : 0

`define QxJjUO7gwXY9    63 : 0
`define tOYw6Vu57dna    63 : 0

`define TxdMFq1xmATA    63 : 0
`define jxA1xJ0ltbz3    63 : 0

`define SxJp3UZJkn      7  : 0

// data-type-bus
`define EpBbZB          2  : 0
`define jJ68vg          3'b001
`define JvOAJc          3'b010 
`define KLtmBa          3'b011 
`define S7jXiy          3'b100 
`define fs775s          3'b101  
`define eM46Y8          3'b110 
`define eZ8gVF          3'b111


// -------------------------------------------------
// ALU_OP
// -------------------------------------------------
// -------------- NOP -----------------
`define iaOgcOZ2T2      8'b0000_0000
// -------------- U-TYPE -----------------
`define CaJAnuiJh8      8'b0010_0000
`define lmypkz6UHWzW    8'b0010_0001
// -------------- J-TYPE -----------------
`define kBLpILR6F6      8'b0100_0000
// -------------- B-TYPE -----------------
`define Uh51CJZ1my      8'b0110_0000
`define uRAWNpvivV      8'b0110_0001
`define sFUXHxtwk9      8'b0110_0010
`define ljF68bMmBk      8'b0110_0011
`define x2qOGrxolQc     8'b0110_0100
`define jtOKAfRTYK0     8'b0110_0101
// ------------- I/R-TYPE ----------------
`define CFtFJ9DBPS      8'b1000_0000
`define gn2exuAbLT      8'b1000_0001
`define pcePm8LD1v      8'b1000_0010
`define TfvHDH83Ohj     8'b1000_0011
`define ILodpBxBQ2      8'b1000_0100
`define u0w1PitU8       8'b1000_0101
`define KZhl8cmB7g      8'b1000_0110
`define Fr1hfyiDMp      8'b1000_0111
`define WPJyCzckVm      8'b1000_1000
`define vso7X0E69R      8'b1000_1001
`define h5VOpvRi5m      8'b1000_1010 
// ------------ Custom --------------------
`define Gtp0AMLiF4c     8'b1011_0000
`define aQR5mt82cTnp    8'b1011_0001


// lwu - I
`define d9cg37nYrr      7'b000_0011
`define GlL51yaZji      3'b110 
// ld - I
`define mVRV0ktrV       7'b000_0011
`define n9SVg4EDp       3'b011 
// sd - S 
`define mNcOlga9V       7'b010_0011 
`define lKwfeYDPI       3'B011 
// addiw
`define VQRrxDDvgJYp    7'b0011011
`define aNZTmc5UNseH    3'b000
// slliw
`define hVzEAOGZQuGj    7'b0011011
`define IR7j7j7QnhjJ    3'b001
`define rN5jjj8hXvn9    7'b0000000
// srliw
`define jlnNPg1qq4kt    7'b0011011
`define PAzJ4esDv6DN    3'b101
`define TatfePyiD9Qu    7'b0000000
// sraiw
`define XmDXi2J4M5CF    7'b0011011
`define EvIaDMBrTRQ6    3'b101
`define fZtER6hkjzih    7'b0100000
// addw 
`define DXx4p5lT8qn     7'b0111011
`define POfc1ziGUfl     3'b000
`define KtzkEEsIxud     7'b0000000
// subw 
`define MiXjMRaSkDi     7'b0111011
`define SEe5P4V1Aos     3'b000
`define O2OSxswRwQg     7'b0100000
// sllw
`define iCCIZlcLp9x     7'b0111011
`define gKeV7xtc4Dx     3'b001
`define RRunInDb8Wy     7'b0000000
// srlw
`define AZjJWntFksw     7'b0111011
`define Xt8EfC9ydD6     3'b101
`define VucCy1o3KZ5     7'b0000000
// sraw
`define VrJQB7hN3mF     7'b0111011
`define t82HJ3Bx2Qe     3'b101
`define cT10E8itqp7     7'b0100000



// -------------------------------------------------
// RV-32I Instruction OPCODE
// -------------------------------------------------
// -------------- U-TYPE -----------------
`define T8IHs6qOe5      7'b011_0111
`define cuweGi4XG655    7'b001_0111
// -------------- J-TYPE -----------------
`define pDmKKZ1fL6      7'b110_1111
`define uBHIuTxYavh     7'b110_0111

`define jdWUy8bflet     3'b000
// -------------- B-TYPE -----------------
`define tRS28MA27f      7'b110_0011
`define gKU0dgkUI1      7'b110_0011
`define aKvJdeQzA5      7'b110_0011
`define qJFkn78bsF      7'b110_0011
`define tgxqfTIeHEA     7'b110_0011
`define hRr7ppwNP4v     7'b110_0011

`define HSSpgDnPVn      3'b000
`define zutYtQDF9o      3'b001
`define m2WeP3dWeH      3'b100
`define ukjryxcYO8      3'b101
`define Fl39tDo3T5u     3'b110
`define qIrIPAXFWRR     3'b111
// ----------- I-TYPE (LOAD)--------------
`define uuZEgIlk6       7'b000_0011
`define ZyCKqkpPv       7'b000_0011
`define ivIw2Wpap       7'b000_0011
`define FmDXdgEBBS      7'b000_0011
`define eGWtKNHBrN      7'b000_0011

`define f4aSoLIAG       3'b000
`define Ali1f0Bq7       3'b001
`define yb0BgGloZ       3'b010
`define NEU9XXrPjK      3'b100
`define AYFvDTiPfB      3'b101
// -------------- S-TYPE -----------------
`define Stx3nWIKx       7'b010_0011 
`define xciFLW7RZ       7'b010_0011 
`define Mvjq76Q6S       7'b010_0011 

`define EH908cKnd       3'b000
`define FybOMDig1       3'b001
`define CBAAeKS7K       3'b010
// -------------- I-TYPE -----------------
`define c07BBelQWOK     7'b001_0011
`define xtwsr7sdduN     7'b001_0011
`define tqOg3zCtfUoF    7'b001_0011
`define u1ibQNYqut4     7'b001_0011
`define g0gY6bslo4      7'b001_0011
`define NgelAmaNn1L     7'b001_0011
`define rSv2gn4vSNV     7'b001_0011
`define ifJrkQnUn6R     7'b001_0011
`define wTy0dfTeHeJ     7'b001_0011

`define YRMMNN4TUdw     3'b000
`define p5Z3p4SegTR     3'b010
`define q5AULrAHQdm8    3'b011
`define JaGLc8smbD1     3'b100
`define n5XxJeLbFK      3'b110
`define SQ81DVUOzVd     3'b111
`define QLzrhnntiV0     3'b001
`define bslI6Xq2wRD     3'b101
`define XAe4jHtFCr8     3'b101

`define Da5pLcS70NR     7'b000_0000 
`define VQrMF7eQgm2     7'b000_0000 
`define hUDiozPiT0f     7'b010_0000 
// -------------- R-TYPE -----------------
`define HzCTuYSh6F      7'b011_0011
`define cmOp8Uzhmx      7'b011_0011
`define b1KmavpBZr      7'b011_0011
`define gLU4Ukqovo      7'b011_0011
`define D9ksH4Rrj5V     7'b011_0011
`define CvqHA8SVtL      7'b011_0011
`define yUY4oVzxaH      7'b011_0011
`define ocoa9WU0KX      7'b011_0011
`define PvCwYnlwg       7'b011_0011
`define ylVUMEwywW      7'b011_0011

`define H4iddvKMb3      3'b000 
`define D4PpwmfCyD      3'b000 
`define d2NQpDdHto      3'b001 
`define kjdFB8ZQ7Q      3'b010 
`define H6DsVcBSdTF     3'b011 
`define LxHLquINCS      3'b100 
`define f1YR1oqBoF      3'b101 
`define lUOq8RtnaP      3'b101 
`define YUFVKngS8       3'b110 
`define Alx8gFXcsl      3'b111 

`define j0RnEVz2Wc      7'b000_0000 
`define Mpx6Q70ovj      7'b010_0000 
`define Qt0GQh80NB      7'b000_0000 
`define a0dQ49DHWf      7'b000_0000 
`define lnggff7sScV     7'b000_0000 
`define ShEZhHPIbG      7'b000_0000 
`define vXkhG6kbHc      7'b000_0000 
`define ZRbml0sc7F      7'b010_0000 
`define coBR7hWgF       7'b000_0000 
`define V0NTilTEr0      7'b000_0000 

// -------------------------------------------------
// RV-32M Instruction OPCODE
// -------------------------------------------------
`define mqEuSUl6ss      7'b011_0011 
`define TU2d0SYyNH      3'b000
`define S8rGXqP8GU      7'b000_0001


`define QL2OgQ      ( GQAE9Cct || QPShFtv4Dw )
`define DVJLEh      ( TuCoUBCD )

`define mdjIGrjq    ( YWBp3rEd || uP7s2O0a || RA58HofI || pX97QCtg )
`define YcwsiAne    ( IDE3hK2uz || ycoCSReVj )
`define Ispggl      ( `mdjIGrjq || `YcwsiAne )

`define WapuyLJe    ( nIiCfXy || INFMfFr || OkydoJc || G4mS4Qzt || YdPZj3Dq || CzgHuUSI || A66ucgr )
`define cAlSZpeT    ( ShXAdPB5O || SzgUUYWIJ || u4KcONyV2 || GbT8XbTzEJ || TYeqOMZzB || hTsfETwt || Pqvfr2zZE || mXoVztmUwG )
`define pJtBSLdx    ( wa0Zzdgim || QjcCGqv8V || qMhxEPDIY || dWa2lYZtw5 || pngOwGCJUg || CTHw1Tw4JF )
`define cR8CDA      ( `WapuyLJe || `cAlSZpeT || `pJtBSLdx )

`define bs1Xd4      ( C7aogzY || voYw5no || wWtUhx4 || Ljqmq49 )

`define dLv2kG      ( wCAmo0T4 || nzJk0CYt || KAZaki8Z || GryHxQYb || tM5lueYO1 || JxgCKb0r || rFrUt3HA || CsytHqHJ || SVALFln || yz2lRBz7 || WChG0j8I || iBoxza85A || sDefmLkP9 || tkNSPBbC4 || vkhuKAwC7 || LYUGKdPgW )

`define z4mkq943tfM ( mXoVztmUwG || iBoxza85A || sDefmLkP9 || tkNSPBbC4 || dWa2lYZtw5 )
`define p81cIAJJ96Ypi8YmcUem    ( pngOwGCJUg || CTHw1Tw4JF || vkhuKAwC7 || LYUGKdPgW )

`define UzhFXUYmmG  ( `Ispggl || `cR8CDA || `bs1Xd4 || `dLv2kG )
`define V07rPg3db2  ( `Ispggl || `bs1Xd4 || `dLv2kG )

`define hutYDOddlK  ( `cR8CDA ) || ( `bs1Xd4 ) || ( `DVJLEh ) || ( `QL2OgQ )

`define RLp38nptZ   ( TuCoUBCD  || ShXAdPB5O )
`define XRV4YK0     ( `QL2OgQ || `DVJLEh || `cR8CDA || `dLv2kG )

`define k2xwLvY0f9C ( ULxdVmE2P || lXMJdEC4Yj )

 module rvcpu( input qRw, input GFv, output cBvbv8J, input [`ObcjYsbppT7SA] tLAz, 
output [`VJPNWZIH6zdse] M42mseGNC, output kwLFq9ece, output [`TxdMFq1xmATA] jyUGZ15cpD, 
input [`jxA1xJ0ltbz3] sZ8K3fVwEi, output yMK6CRBBo, output [`TxdMFq1xmATA] to7CMZaPKY, 
output [`jxA1xJ0ltbz3] iIaC58CiHx, output [`jxA1xJ0ltbz3] GtC0qmiUqL, output [`muFKlpd] 
imsnW0[0 : 31] ); wire M0tlFLtwNj4 ; wire [`VJPNWZIH6zdse] epavvrInwBl ; wire [`VJPNWZIH6zdse] 
siZbFhceze ; wire [`muFKlpd] X5fLoFTh ; wire plyN9CKEI ; wire [`bDsIxaO3BVSK] kbB098u7Fq 
; wire [`muFKlpd] zxkiTuCK4RD4 ; wire hUduYqCAo0XLk ; wire [`bDsIxaO3BVSK] P9crpNurNqLiBT 
; wire [`muFKlpd] MrCY1P97 ; wire stsrseQpu ; wire [`bDsIxaO3BVSK] Ofs9NJ36Pq ; wire 
[`muFKlpd] BvitSgq1Hyw ; wire [`muFKlpd] Lv5ku5dc93v ; wire [`SxJp3UZJkn] FsjRBjnjb1 
; wire Ai9EtdpBAt ; wire [`muFKlpd] Zsgxv3MTUv ; wire D7oya5Igy ; wire [`VJPNWZIH6zdse] 
gY53pm6Uc1a5z ; wire wWjUJ9g ; wire [`VJPNWZIH6zdse] BFWC5DLl4Ec ; wire [`EpBbZB] 
Yb5ekIkGnKG8c ; wire a4SqTqUk3 ; wire [`tOYw6Vu57dna] VOpQv3szsL ; wire NpNDeHiyF 
; wire [`tOYw6Vu57dna] Sw9ZCULOy5 ; wire z0MsdO ; wire P6wD5b ; wire [`bDsIxaO3BVSK] 
ttyiClU ; wire CP5BekSY; wire [`bDsIxaO3BVSK] fjN1GhT7w; wire [`muFKlpd] cSWijWYsS;
 asoBsvxYb5 eD5A2ng0( .qRw ( qRw ), .GFv ( GFv ), .FlVzqaJLmQB9d ( M0tlFLtwNj4 ), 
.ckxHDsjx2w8VQ ( epavvrInwBl ), .Z1IaB2AXzqhY ( siZbFhceze ), .qovYvzF6D ( cBvbv8J 
), .kXyFYs2fUMl ( M42mseGNC ) ); XmPXT8Z11mb s7o2FNmy( .GFv ( GFv ), .QQqE0R ( tLAz 
), .nZgfwYMPis ( zxkiTuCK4RD4 ), .Nsz4lbgOeJV ( hUduYqCAo0XLk ), .wcqU5ueTTMKp ( 
P9crpNurNqLiBT), .f3HkVuZKqF ( MrCY1P97 ), .w36wdnIqkqw ( stsrseQpu ), .kRMWeuBKEmIC 
( Ofs9NJ36Pq ), .b5FgibAO1U4M ( FsjRBjnjb1 ), .MShTPjvautFo ( Ai9EtdpBAt ), .LfmmAVsUOAh3l 
( BvitSgq1Hyw ), .DOHla3yUfRhRC ( Lv5ku5dc93v ), .MB7uIai8Vtn ( D7oya5Igy ), .Ij1WIf77YjMCk0P(
 gY53pm6Uc1a5z ), .jKbhLHOmf ( wWjUJ9g ), .gyJsJtTcJbZkb ( BFWC5DLl4Ec ), .Sllh1Ezp9IZIqYa(
 Yb5ekIkGnKG8c ), .oJ9dSqzdtZM ( a4SqTqUk3 ), .tWIXk2nzYYR ( NpNDeHiyF ), .U8nHEbYumypl 
( Sw9ZCULOy5 ), .BECXQ3iC ( z0MsdO ), .pGYEoK97 ( P6wD5b ), .OA8Py3qa6 ( ttyiClU 
) ); R2MIbel yO7QPsT0y( .qRw ( qRw ), .GFv ( GFv ), .QJ9hkSUr86g6 ( FsjRBjnjb1 ), 
.Pfjw95PQlMmB ( Ai9EtdpBAt ), .rgW3lMDkqyNwd ( BvitSgq1Hyw ), .fUFVvRbkqOOVT ( Lv5ku5dc93v 
), .hq6Uv7FRgdqu ( Zsgxv3MTUv ), .dKWbtlnVzZKY ( siZbFhceze ), .IVQYajcaky2 ( D7oya5Igy 
), .iBY1wF465kZc43I( gY53pm6Uc1a5z ), .REAeHbGOa ( wWjUJ9g ), .Fg0YJm19jg6un ( BFWC5DLl4Ec 
), .OUTEggu4pjvZO ( M0tlFLtwNj4 ), .JDqNhAjgmujRj ( epavvrInwBl ) ); ckI jjI7ZAzN2(
 .GFv ( GFv ), .UQX1UO534Eueufy( Yb5ekIkGnKG8c ), .aTzikFmnw2L ( a4SqTqUk3 ), .pfoWBIl3BSS8 
( Zsgxv3MTUv ), .RQrXFv01nm2Y ( VOpQv3szsL ), .SzK7f7TPjfs ( NpNDeHiyF ), .dRXdVXnEGkNt 
( Zsgxv3MTUv ), .EVQRdLZoqqwb ( Sw9ZCULOy5 ), .doSBaROEalm ( kwLFq9ece ), .yNuo2ExZSHTt 
( sZ8K3fVwEi ), .XRqKZqMadYIS ( jyUGZ15cpD ), .fiSv7mavUEF ( yMK6CRBBo ), .ySkI99UlzaRl 
( to7CMZaPKY ), .uPLAeJWwH2VD ( iIaC58CiHx ), .mO5xfkjIYekM ( GtC0qmiUqL ) ); sSd2AP5GTD 
Kv7xLhcA( .GFv ( GFv ), .WSDwRmyT ( z0MsdO ), .bqrnOOkRP ( ttyiClU ), .KQRQqPG3 (
 P6wD5b ), .o6ntSojFkw ( Zsgxv3MTUv ), .j3R14S5oI4 ( VOpQv3szsL ), .BECXQ3iC ( CP5BekSY 
), .OA8Py3qa6 ( fjN1GhT7w ), .O9OWRNtHh ( cSWijWYsS ) ); assign plyN9CKEI = hUduYqCAo0XLk 
; assign kbB098u7Fq = P9crpNurNqLiBT ; assign zxkiTuCK4RD4 = X5fLoFTh ; H9w0vao juyxmMf(
 .qRw ( qRw ), .GFv ( GFv ), .tJPZEp4r ( fjN1GhT7w ), .UADGd6uM ( cSWijWYsS ), .jMvw26B 
( CP5BekSY ), .iySWeb57 ( plyN9CKEI ), .qAHqrjuoy ( kbB098u7Fq ), .Y6GtStlxd ( X5fLoFTh 
), .iU9eTHOE ( stsrseQpu ), .cjwWUjvzA ( Ofs9NJ36Pq ), .en8PtIPsi ( MrCY1P97 ), .imsnW0 
( imsnW0 ) ); endmodule module sSd2AP5GTD ( input GFv, input WSDwRmyT, input [`bDsIxaO3BVSK] 
bqrnOOkRP, input KQRQqPG3, input [`muFKlpd] o6ntSojFkw, input [`muFKlpd] j3R14S5oI4, 
output BECXQ3iC, output [`bDsIxaO3BVSK] OA8Py3qa6, output [`muFKlpd] O9OWRNtHh );
 assign BECXQ3iC = (GFv == 1'b1) ? 0 : WSDwRmyT ; assign OA8Py3qa6 = (GFv == 1'b1)
 ? 0 : bqrnOOkRP ; assign O9OWRNtHh = (GFv == 1'b1) ? 0 : (KQRQqPG3) ? j3R14S5oI4 
: o6ntSojFkw ; endmodule module H9w0vao( input qRw, input GFv, input [`bDsIxaO3BVSK] 
tJPZEp4r, input [`muFKlpd] UADGd6uM, input jMvw26B, input iySWeb57, input [`bDsIxaO3BVSK] 
qAHqrjuoy, output reg [`muFKlpd] Y6GtStlxd, input iU9eTHOE, input [`bDsIxaO3BVSK] 
cjwWUjvzA, output reg [`muFKlpd] en8PtIPsi, output wire [`muFKlpd] imsnW0[0 : 31] 
); integer H ; reg [`muFKlpd] oxMw [31 : 0]; always @(posedge qRw ) begin if( GFv 
== 1'b1 ) begin oxMw[0] <= `z16APu5lI ; for(H=1; H<32; H=H+1) begin oxMw[H] <= `z16APu5lI 
; end end else begin if( (jMvw26B == 1'b1) && (tJPZEp4r != 0) ) begin oxMw[tJPZEp4r] 
<= UADGd6uM ; end end end always @( * ) begin if( GFv == 1'b1 ) begin Y6GtStlxd = 
`z16APu5lI ; end else if( iySWeb57 ) begin Y6GtStlxd = oxMw[qAHqrjuoy]; end else 
begin Y6GtStlxd = `z16APu5lI; end end always @( * ) begin if( GFv == 1'b1 ) begin 
en8PtIPsi = `z16APu5lI ; end else if( iU9eTHOE ) begin en8PtIPsi = oxMw[cjwWUjvzA];
 end else begin en8PtIPsi = `z16APu5lI; end end genvar y; generate for (y = 0; y 
< 32; y = y + 1) begin assign imsnW0[y] = (jMvw26B & tJPZEp4r == y & y != 0) ? UADGd6uM 
: oxMw[y]; end endgenerate endmodule module ckI ( input GFv, input [`EpBbZB] UQX1UO534Eueufy, 
input aTzikFmnw2L, input [`QxJjUO7gwXY9] pfoWBIl3BSS8, output [`tOYw6Vu57dna] RQrXFv01nm2Y, 
input SzK7f7TPjfs, input [`QxJjUO7gwXY9] dRXdVXnEGkNt, input [`tOYw6Vu57dna] EVQRdLZoqqwb, 
output doSBaROEalm, input [`jxA1xJ0ltbz3] yNuo2ExZSHTt, output [`TxdMFq1xmATA] XRqKZqMadYIS, 
output fiSv7mavUEF, output [`TxdMFq1xmATA] ySkI99UlzaRl, output [`jxA1xJ0ltbz3] uPLAeJWwH2VD, 
output [`jxA1xJ0ltbz3] mO5xfkjIYekM ); wire [63:0] shKCUQ82VLz ; wire [31:0] sKJju8Xgr7C 
; wire [15:0] bEdFnqpLQFY ; wire [7 :0] ZxFtpStHVWm ; assign shKCUQ82VLz = yNuo2ExZSHTt[63: 
0]; assign sKJju8Xgr7C = pfoWBIl3BSS8[2] ? shKCUQ82VLz[63:32] : shKCUQ82VLz[31: 0] 
; assign bEdFnqpLQFY = pfoWBIl3BSS8[1] ? sKJju8Xgr7C[31:16] : sKJju8Xgr7C[15: 0] 
; assign ZxFtpStHVWm = pfoWBIl3BSS8[0] ? bEdFnqpLQFY[15: 8] : bEdFnqpLQFY[ 7: 0] 
; assign doSBaROEalm = aTzikFmnw2L ; assign XRqKZqMadYIS = pfoWBIl3BSS8 ; assign 
RQrXFv01nm2Y = (UQX1UO534Eueufy == `jJ68vg) ? { {(64-8){ZxFtpStHVWm[7]}}, ZxFtpStHVWm 
} : (UQX1UO534Eueufy == `S7jXiy) ? { {(64-8){1'b0}}, ZxFtpStHVWm } : (UQX1UO534Eueufy 
== `JvOAJc) ? { {(64-16){bEdFnqpLQFY[15]}}, bEdFnqpLQFY } : (UQX1UO534Eueufy == `fs775s)
 ? { {(64-16){1'b0}}, bEdFnqpLQFY } : (UQX1UO534Eueufy == `KLtmBa) ? { {(64-32){sKJju8Xgr7C[31]}}, 
sKJju8Xgr7C } : (UQX1UO534Eueufy == `eM46Y8) ? { {(64-32){1'b0}}, sKJju8Xgr7C } : 
(UQX1UO534Eueufy == `eZ8gVF) ? { shKCUQ82VLz } : 0; wire [63:0] KzUS4LT1bsC ; wire 
[63:0] gHxuxUy7K ; wire [63:0] iKFW5SVLk ; wire [63:0] bNoqmsE88 ; wire [63:0] iMPPekqeo 
; assign gHxuxUy7K = 64'hffff_ffff_ffff_ffff ; assign iKFW5SVLk = ( dRXdVXnEGkNt[2] 
) ? 64'hffff_ffff_0000_0000 : 64'h0000_0000_ffff_ffff ; assign bNoqmsE88 = ( dRXdVXnEGkNt[2:1] 
== 2'b00 ) ? 64'h0000_0000_0000_ffff : ( dRXdVXnEGkNt[2:1] == 2'b01 ) ? 64'h0000_0000_ffff_0000 
: ( dRXdVXnEGkNt[2:1] == 2'b10 ) ? 64'h0000_ffff_0000_0000 : ( dRXdVXnEGkNt[2:1] 
== 2'b11 ) ? 64'hffff_0000_0000_0000 : 64'h0000_0000_0000_0000 ; assign iMPPekqeo 
= ( dRXdVXnEGkNt[2:0] == 3'b000 ) ? 64'h0000_0000_0000_00ff : ( dRXdVXnEGkNt[2:0] 
== 3'b001 ) ? 64'h0000_0000_0000_ff00 : ( dRXdVXnEGkNt[2:0] == 3'b010 ) ? 64'h0000_0000_00ff_0000 
: ( dRXdVXnEGkNt[2:0] == 3'b011 ) ? 64'h0000_0000_ff00_0000 : ( dRXdVXnEGkNt[2:0] 
== 3'b100 ) ? 64'h0000_00ff_0000_0000 : ( dRXdVXnEGkNt[2:0] == 3'b101 ) ? 64'h0000_ff00_0000_0000 
: ( dRXdVXnEGkNt[2:0] == 3'b110 ) ? 64'h00ff_0000_0000_0000 : ( dRXdVXnEGkNt[2:0] 
== 3'b111 ) ? 64'hff00_0000_0000_0000 : 64'h0000_0000_0000_0000 ; assign uPLAeJWwH2VD 
= (UQX1UO534Eueufy == `jJ68vg) ? (EVQRdLZoqqwb << ((dRXdVXnEGkNt[2:0])*8)) : (UQX1UO534Eueufy 
== `JvOAJc) ? (EVQRdLZoqqwb << ((dRXdVXnEGkNt[2:1])*16)) : (UQX1UO534Eueufy == `KLtmBa)
 ? (EVQRdLZoqqwb << ((dRXdVXnEGkNt[2])*32)) : (UQX1UO534Eueufy == `eZ8gVF) ? (EVQRdLZoqqwb)
 : 64'h0000_0000_0000_0000 ; assign fiSv7mavUEF = SzK7f7TPjfs ; assign ySkI99UlzaRl 
= dRXdVXnEGkNt ; assign KzUS4LT1bsC = fiSv7mavUEF ? (UQX1UO534Eueufy == `jJ68vg) 
? iMPPekqeo : (UQX1UO534Eueufy == `JvOAJc) ? bNoqmsE88 : (UQX1UO534Eueufy == `KLtmBa)
 ? iKFW5SVLk : (UQX1UO534Eueufy == `eZ8gVF) ? gHxuxUy7K : 64'h0000_0000_0000_0000 
: 64'h0000_0000_0000_0000 ; assign mO5xfkjIYekM = KzUS4LT1bsC ; endmodule module 
asoBsvxYb5( input qRw, input GFv, input FlVzqaJLmQB9d, input [`VJPNWZIH6zdse] ckxHDsjx2w8VQ, 
output [`VJPNWZIH6zdse] Z1IaB2AXzqhY, output qovYvzF6D, output [`VJPNWZIH6zdse] kXyFYs2fUMl 
); reg [`VJPNWZIH6zdse] db ; reg [`VJPNWZIH6zdse] nlju ; always @( posedge qRw ) 
begin if( GFv == 1'b1 ) begin db <= `hJZJwMU5 ; nlju <= `hJZJwMU5 ; end else begin 
if( FlVzqaJLmQB9d ) begin db <= ckxHDsjx2w8VQ + 4 ; nlju <= ckxHDsjx2w8VQ ; end else 
begin db <= db + 4 ; nlju <= db ; end end end assign Z1IaB2AXzqhY = nlju ; assign 
kXyFYs2fUMl = nlju ; assign qovYvzF6D = ( GFv != 1'b1 ) ; endmodule module XmPXT8Z11mb(
 input GFv, input [`ObcjYsbppT7SA] QQqE0R, input [`muFKlpd] nZgfwYMPis, output Nsz4lbgOeJV, 
output [`bDsIxaO3BVSK] wcqU5ueTTMKp, input [`muFKlpd] f3HkVuZKqF, output w36wdnIqkqw, 
output [`bDsIxaO3BVSK] kRMWeuBKEmIC, output [`SxJp3UZJkn] b5FgibAO1U4M, output MShTPjvautFo, 
output [`muFKlpd] LfmmAVsUOAh3l, output [`muFKlpd] DOHla3yUfRhRC, output MB7uIai8Vtn, 
output [`VJPNWZIH6zdse] Ij1WIf77YjMCk0P, output jKbhLHOmf, output [`VJPNWZIH6zdse] 
gyJsJtTcJbZkb, output [`EpBbZB] Sllh1Ezp9IZIqYa, output oJ9dSqzdtZM, output tWIXk2nzYYR, 
output [`tOYw6Vu57dna] U8nHEbYumypl, output BECXQ3iC, output pGYEoK97, output [`bDsIxaO3BVSK] 
OA8Py3qa6 ); wire [31 : 0] ZgK2w ; wire [20 : 0] R9GYY ; wire [12 : 0] bEPRS ; wire 
[11 : 0] ljuWe ; wire [11 : 0] ZWT7B ; wire [6 : 0] v212gv ; wire [4 : 0] pI8; wire 
[4 : 0] z04; wire [2 : 0] I5AJbs; wire [4 : 0] nb; wire [6 : 0] gfIyT7; wire [`muFKlpd] 
Sqk7XaBzon2D9v ; wire GQAE9Cct ; wire QPShFtv4Dw ; wire TuCoUBCD ; wire ShXAdPB5O 
; wire YWBp3rEd ; wire uP7s2O0a ; wire RA58HofI ; wire pX97QCtg ; wire IDE3hK2uz 
; wire ycoCSReVj ; wire nIiCfXy ; wire INFMfFr ; wire OkydoJc ; wire G4mS4Qzt ; wire 
YdPZj3Dq ; wire C7aogzY ; wire voYw5no ; wire wWtUhx4 ; wire SzgUUYWIJ ; wire u4KcONyV2 
; wire GbT8XbTzEJ ; wire TYeqOMZzB ; wire hTsfETwt ; wire Pqvfr2zZE ; wire wa0Zzdgim 
; wire QjcCGqv8V ; wire qMhxEPDIY ; wire wCAmo0T4 ; wire nzJk0CYt ; wire KAZaki8Z 
; wire GryHxQYb ; wire tM5lueYO1 ; wire JxgCKb0r ; wire rFrUt3HA ; wire CsytHqHJ 
; wire SVALFln ; wire yz2lRBz7 ; wire WChG0j8I ; wire CzgHuUSI ; wire A66ucgr ; wire 
Ljqmq49 ; wire mXoVztmUwG ; wire dWa2lYZtw5 ; wire pngOwGCJUg ; wire CTHw1Tw4JF ;
 wire iBoxza85A ; wire sDefmLkP9 ; wire tkNSPBbC4 ; wire vkhuKAwC7 ; wire LYUGKdPgW 
; wire ULxdVmE2P ; wire lXMJdEC4Yj ; assign ZgK2w = { QQqE0R[31:12], 12'd0 } ; assign 
R9GYY = { QQqE0R[31], QQqE0R[19:12], QQqE0R[20], QQqE0R[30:21], 1'b0 } ; assign bEPRS 
= { QQqE0R[31], QQqE0R[7], QQqE0R[30:25], QQqE0R[11:8], 1'b0 }; assign ljuWe = { 
QQqE0R[31:25], QQqE0R[11:7] } ; assign ZWT7B = QQqE0R[ 31 : 20 ] ; assign v212gv 
= QQqE0R[ 31 : 25 ] ; assign pI8 = QQqE0R[ 24 : 20 ] ; assign z04 = QQqE0R[ 19 : 
15 ] ; assign I5AJbs = QQqE0R[ 14 : 12 ] ; assign nb = QQqE0R[ 11 : 7 ] ; assign 
gfIyT7 = QQqE0R[ 6 : 0 ] ; assign GQAE9Cct = ( gfIyT7 == `T8IHs6qOe5 ) ; assign QPShFtv4Dw 
= ( gfIyT7 == `cuweGi4XG655) ; assign TuCoUBCD = ( gfIyT7 == `pDmKKZ1fL6 ) ; assign 
ShXAdPB5O = ( gfIyT7 == `uBHIuTxYavh ) & ( I5AJbs == `jdWUy8bflet ) ; assign YWBp3rEd 
= ( gfIyT7 == `tRS28MA27f ) & ( I5AJbs == `HSSpgDnPVn ) ; assign uP7s2O0a = ( gfIyT7 
== `gKU0dgkUI1 ) & ( I5AJbs == `zutYtQDF9o ) ; assign RA58HofI = ( gfIyT7 == `aKvJdeQzA5 
) & ( I5AJbs == `m2WeP3dWeH ) ; assign pX97QCtg = ( gfIyT7 == `qJFkn78bsF ) & ( I5AJbs 
== `ukjryxcYO8 ) ; assign IDE3hK2uz = ( gfIyT7 == `tgxqfTIeHEA ) & ( I5AJbs == `Fl39tDo3T5u 
) ; assign ycoCSReVj = ( gfIyT7 == `hRr7ppwNP4v ) & ( I5AJbs == `qIrIPAXFWRR ) ; 
assign nIiCfXy = ( gfIyT7 == `uuZEgIlk6 ) & ( I5AJbs == `f4aSoLIAG ) ; assign INFMfFr 
= ( gfIyT7 == `ZyCKqkpPv ) & ( I5AJbs == `Ali1f0Bq7 ) ; assign OkydoJc = ( gfIyT7 
== `ivIw2Wpap ) & ( I5AJbs == `yb0BgGloZ ) ; assign G4mS4Qzt = ( gfIyT7 == `FmDXdgEBBS 
) & ( I5AJbs == `NEU9XXrPjK ) ; assign YdPZj3Dq = ( gfIyT7 == `eGWtKNHBrN ) & ( I5AJbs 
== `AYFvDTiPfB ) ; assign C7aogzY = ( gfIyT7 == `Stx3nWIKx ) & ( I5AJbs == `EH908cKnd 
) ; assign voYw5no = ( gfIyT7 == `xciFLW7RZ ) & ( I5AJbs == `FybOMDig1 ) ; assign 
wWtUhx4 = ( gfIyT7 == `Mvjq76Q6S ) & ( I5AJbs == `CBAAeKS7K ) ; assign SzgUUYWIJ 
= ( gfIyT7 == `c07BBelQWOK ) & ( I5AJbs == `YRMMNN4TUdw ) ; assign u4KcONyV2 = ( 
gfIyT7 == `xtwsr7sdduN ) & ( I5AJbs == `p5Z3p4SegTR ) ; assign GbT8XbTzEJ = ( gfIyT7 
== `tqOg3zCtfUoF) & ( I5AJbs == `q5AULrAHQdm8) ; assign TYeqOMZzB = ( gfIyT7 == `u1ibQNYqut4 
) & ( I5AJbs == `JaGLc8smbD1 ) ; assign hTsfETwt = ( gfIyT7 == `g0gY6bslo4 ) & ( 
I5AJbs == `n5XxJeLbFK ) ; assign Pqvfr2zZE = ( gfIyT7 == `NgelAmaNn1L ) & ( I5AJbs 
== `SQ81DVUOzVd ) ; assign wCAmo0T4 = ( gfIyT7 == `HzCTuYSh6F ) & ( I5AJbs == `H4iddvKMb3 
) & ( v212gv == `j0RnEVz2Wc ) ; assign nzJk0CYt = ( gfIyT7 == `cmOp8Uzhmx ) & ( I5AJbs 
== `D4PpwmfCyD ) & ( v212gv == `Mpx6Q70ovj ) ; assign KAZaki8Z = ( gfIyT7 == `b1KmavpBZr 
) & ( I5AJbs == `d2NQpDdHto ) & ( v212gv == `Qt0GQh80NB ) ; assign GryHxQYb = ( gfIyT7 
== `gLU4Ukqovo ) & ( I5AJbs == `kjdFB8ZQ7Q ) & ( v212gv == `a0dQ49DHWf ) ; assign 
tM5lueYO1 = ( gfIyT7 == `D9ksH4Rrj5V ) & ( I5AJbs == `H6DsVcBSdTF ) & ( v212gv == 
`lnggff7sScV) ; assign JxgCKb0r = ( gfIyT7 == `CvqHA8SVtL ) & ( I5AJbs == `LxHLquINCS 
) & ( v212gv == `ShEZhHPIbG ) ; assign rFrUt3HA = ( gfIyT7 == `yUY4oVzxaH ) & ( I5AJbs 
== `f1YR1oqBoF ) & ( v212gv == `vXkhG6kbHc ) ; assign CsytHqHJ = ( gfIyT7 == `ocoa9WU0KX 
) & ( I5AJbs == `lUOq8RtnaP ) & ( v212gv == `ZRbml0sc7F ) ; assign SVALFln = ( gfIyT7 
== `PvCwYnlwg ) & ( I5AJbs == `YUFVKngS8 ) & ( v212gv == `coBR7hWgF ) ; assign yz2lRBz7 
= ( gfIyT7 == `ylVUMEwywW ) & ( I5AJbs == `Alx8gFXcsl ) & ( v212gv == `V0NTilTEr0 
) ; assign WChG0j8I = ( gfIyT7 == `mqEuSUl6ss ) & ( I5AJbs == `TU2d0SYyNH ) & ( v212gv 
== `S8rGXqP8GU ) ; assign CzgHuUSI = ( gfIyT7 == `d9cg37nYrr ) & ( I5AJbs == `GlL51yaZji 
) ; assign A66ucgr = ( gfIyT7 == `mVRV0ktrV ) & ( I5AJbs == `n9SVg4EDp ) ; assign 
Ljqmq49 = ( gfIyT7 == `mNcOlga9V ) & ( I5AJbs == `lKwfeYDPI ) ; assign wa0Zzdgim 
= ( gfIyT7 == `rSv2gn4vSNV ) & ( I5AJbs == `QLzrhnntiV0 ) & ( {v212gv[6:1], 1'b0} 
== `Da5pLcS70NR) ; assign QjcCGqv8V = ( gfIyT7 == `ifJrkQnUn6R ) & ( I5AJbs == `bslI6Xq2wRD 
) & ( {v212gv[6:1], 1'b0} == `VQrMF7eQgm2) ; assign qMhxEPDIY = ( gfIyT7 == `wTy0dfTeHeJ 
) & ( I5AJbs == `XAe4jHtFCr8 ) & ( {v212gv[6:1], 1'b0} == `hUDiozPiT0f) ; assign 
mXoVztmUwG = ( gfIyT7 == `VQRrxDDvgJYp ) & ( I5AJbs == `aNZTmc5UNseH ) ; assign dWa2lYZtw5 
= ( gfIyT7 == `hVzEAOGZQuGj ) & ( I5AJbs == `IR7j7j7QnhjJ ) & ( v212gv == `rN5jjj8hXvn9)
 ; assign pngOwGCJUg = ( gfIyT7 == `jlnNPg1qq4kt ) & ( I5AJbs == `PAzJ4esDv6DN ) 
& ( v212gv == `TatfePyiD9Qu) ; assign CTHw1Tw4JF = ( gfIyT7 == `XmDXi2J4M5CF ) & 
( I5AJbs == `EvIaDMBrTRQ6 ) & ( v212gv == `fZtER6hkjzih) ; assign iBoxza85A = ( gfIyT7 
== `DXx4p5lT8qn ) & ( I5AJbs == `POfc1ziGUfl ) & ( v212gv == `KtzkEEsIxud ) ; assign 
sDefmLkP9 = ( gfIyT7 == `MiXjMRaSkDi ) & ( I5AJbs == `SEe5P4V1Aos ) & ( v212gv == 
`O2OSxswRwQg ) ; assign tkNSPBbC4 = ( gfIyT7 == `iCCIZlcLp9x ) & ( I5AJbs == `gKeV7xtc4Dx 
) & ( v212gv == `RRunInDb8Wy) ; assign vkhuKAwC7 = ( gfIyT7 == `AZjJWntFksw ) & (
 I5AJbs == `Xt8EfC9ydD6 ) & ( v212gv == `VucCy1o3KZ5) ; assign LYUGKdPgW = ( gfIyT7 
== `VrJQB7hN3mF ) & ( I5AJbs == `t82HJ3Bx2Qe ) & ( v212gv == `cT10E8itqp7) ; assign 
ULxdVmE2P = ( QQqE0R == 32'h0000006b ); assign lXMJdEC4Yj = ( QQqE0R == 32'h0005007f 
); assign b5FgibAO1U4M = ( GFv == 1'b1 ) ? `iaOgcOZ2T2 : ( GQAE9Cct ) ? `CaJAnuiJh8 
: ( QPShFtv4Dw ) ? `lmypkz6UHWzW : ( TuCoUBCD ) ? `kBLpILR6F6 : ( ShXAdPB5O ) ? `CFtFJ9DBPS 
: ( YWBp3rEd ) ? `Uh51CJZ1my : ( uP7s2O0a ) ? `uRAWNpvivV : ( RA58HofI ) ? `sFUXHxtwk9 
: ( pX97QCtg ) ? `ljF68bMmBk : ( IDE3hK2uz ) ? `x2qOGrxolQc : ( ycoCSReVj ) ? `jtOKAfRTYK0 
: ( nIiCfXy ) ? `CFtFJ9DBPS : ( INFMfFr ) ? `CFtFJ9DBPS : ( OkydoJc ) ? `CFtFJ9DBPS 
: ( G4mS4Qzt ) ? `CFtFJ9DBPS : ( YdPZj3Dq ) ? `CFtFJ9DBPS : ( C7aogzY ) ? `CFtFJ9DBPS 
: ( voYw5no ) ? `CFtFJ9DBPS : ( wWtUhx4 ) ? `CFtFJ9DBPS : ( SzgUUYWIJ ) ? `CFtFJ9DBPS 
: ( u4KcONyV2 ) ? `pcePm8LD1v : ( GbT8XbTzEJ ) ? `TfvHDH83Ohj : ( TYeqOMZzB ) ? `ILodpBxBQ2 
: ( hTsfETwt ) ? `u0w1PitU8 : ( Pqvfr2zZE ) ? `KZhl8cmB7g : ( wa0Zzdgim ) ? `Fr1hfyiDMp 
: ( QjcCGqv8V ) ? `WPJyCzckVm : ( qMhxEPDIY ) ? `vso7X0E69R : ( wCAmo0T4 ) ? `CFtFJ9DBPS 
: ( nzJk0CYt ) ? `gn2exuAbLT : ( KAZaki8Z ) ? `Fr1hfyiDMp : ( GryHxQYb ) ? `pcePm8LD1v 
: ( tM5lueYO1 ) ? `TfvHDH83Ohj : ( JxgCKb0r ) ? `ILodpBxBQ2 : ( rFrUt3HA ) ? `WPJyCzckVm 
: ( CsytHqHJ ) ? `vso7X0E69R : ( SVALFln ) ? `u0w1PitU8 : ( yz2lRBz7 ) ? `KZhl8cmB7g 
: ( WChG0j8I ) ? `h5VOpvRi5m : ( CzgHuUSI ) ? `CFtFJ9DBPS : ( A66ucgr ) ? `CFtFJ9DBPS 
: ( Ljqmq49 ) ? `CFtFJ9DBPS : ( mXoVztmUwG ) ? `CFtFJ9DBPS : ( dWa2lYZtw5 ) ? `Fr1hfyiDMp 
: ( pngOwGCJUg ) ? `WPJyCzckVm : ( CTHw1Tw4JF ) ? `vso7X0E69R : ( iBoxza85A ) ? `CFtFJ9DBPS 
: ( sDefmLkP9 ) ? `gn2exuAbLT : ( tkNSPBbC4 ) ? `Fr1hfyiDMp : ( vkhuKAwC7 ) ? `WPJyCzckVm 
: ( LYUGKdPgW ) ? `vso7X0E69R : ( ULxdVmE2P ) ? `Gtp0AMLiF4c : ( lXMJdEC4Yj ) ? `aQR5mt82cTnp 
: `iaOgcOZ2T2 ; assign MShTPjvautFo = ( `z4mkq943tfM ) ? 1'b1 : 1'b0 ; assign Nsz4lbgOeJV 
= ( GFv != 1'b1 ) & ( `UzhFXUYmmG || `k2xwLvY0f9C ) ; assign wcqU5ueTTMKp = ( `UzhFXUYmmG 
) ? z04 : ( `k2xwLvY0f9C ) ? 5'd10 : 0 ; assign w36wdnIqkqw = ( GFv != 1'b1 ) & (
 `V07rPg3db2 || `k2xwLvY0f9C ); assign kRMWeuBKEmIC = ( `V07rPg3db2 ) ? pI8 : ( `k2xwLvY0f9C 
) ? 5'd10 : 0 ; assign Sqk7XaBzon2D9v = ( `cAlSZpeT ) ? { {(64-12){ZWT7B[11]}} , 
ZWT7B } : ( `WapuyLJe ) ? { {(64-12){ZWT7B[11]}} , ZWT7B } : ( `pJtBSLdx ) ? { {(
64-6){1'b0}} , ZWT7B[5:0] } : ( `bs1Xd4 ) ? { {(64-12){ljuWe[11]}} , ljuWe } : ( 
`DVJLEh ) ? { {(64-21){R9GYY[20]}} , R9GYY } : ( `QL2OgQ ) ? { {(64-32){ZgK2w[31]}} 
, ZgK2w } : {(64){1'b0}} ; assign LfmmAVsUOAh3l = ( Nsz4lbgOeJV ) ? ( (pngOwGCJUg 
|| vkhuKAwC7) ? {{(32){1'b0}},nZgfwYMPis[31:0]} : (CTHw1Tw4JF || LYUGKdPgW) ? {{(
32){nZgfwYMPis[31]}},nZgfwYMPis[31:0]} : nZgfwYMPis ) : 0 ; assign DOHla3yUfRhRC 
= ( `hutYDOddlK ) ? Sqk7XaBzon2D9v : ( w36wdnIqkqw ) ? ((`p81cIAJJ96Ypi8YmcUem) ? 
{59'd0, f3HkVuZKqF[4:0]} : f3HkVuZKqF ) : 0 ; assign MB7uIai8Vtn = ( `Ispggl ) ; 
assign Ij1WIf77YjMCk0P = ( `Ispggl ) ? { {(64-13){bEPRS[12]}}, bEPRS } : 0 ; assign 
jKbhLHOmf = ( `RLp38nptZ ) ; assign gyJsJtTcJbZkb = ( `RLp38nptZ ) ? { {(64-21){R9GYY[20]}}, 
R9GYY } : 0 ; assign Sllh1Ezp9IZIqYa = ( nIiCfXy || C7aogzY ) ? `jJ68vg : ( G4mS4Qzt 
) ? `S7jXiy : ( INFMfFr || voYw5no ) ? `JvOAJc : ( YdPZj3Dq ) ? `fs775s : ( OkydoJc 
|| wWtUhx4 ) ? `KLtmBa : ( CzgHuUSI ) ? `eM46Y8 : ( A66ucgr || Ljqmq49 ) ? `eZ8gVF 
: 0 ; assign oJ9dSqzdtZM = ( `WapuyLJe ) ? 1'b1 : 0 ; assign tWIXk2nzYYR = ( `bs1Xd4 
) ? 1'b1 : 0 ; assign U8nHEbYumypl = ( `bs1Xd4 ) ? f3HkVuZKqF : 0 ; assign BECXQ3iC 
= ( `XRV4YK0 ); assign pGYEoK97 = ( `WapuyLJe ) ? 1'b1 : 0 ; assign OA8Py3qa6 = (
 `XRV4YK0 ) ? nb : 0 ; endmodule module R2MIbel( input qRw, input GFv, input [`SxJp3UZJkn] 
QJ9hkSUr86g6, input Pfjw95PQlMmB, input [`muFKlpd] rgW3lMDkqyNwd, input [`muFKlpd] 
fUFVvRbkqOOVT, output [`muFKlpd] hq6Uv7FRgdqu, input [`VJPNWZIH6zdse] dKWbtlnVzZKY, 
input IVQYajcaky2, input [`VJPNWZIH6zdse] iBY1wF465kZc43I, input REAeHbGOa, input 
[`VJPNWZIH6zdse] Fg0YJm19jg6un, output OUTEggu4pjvZO, output [`VJPNWZIH6zdse] JDqNhAjgmujRj 
); reg [`muFKlpd] ms6BV1DMBx ; wire Ha5GWqunguUc ; always @(*) begin if( GFv == 1'b1 
) begin ms6BV1DMBx = 0 ; end else begin case ( QJ9hkSUr86g6 ) `CaJAnuiJh8: ms6BV1DMBx 
= ( fUFVvRbkqOOVT ) ; `lmypkz6UHWzW: ms6BV1DMBx = ( dKWbtlnVzZKY + fUFVvRbkqOOVT 
) ; `kBLpILR6F6: ms6BV1DMBx = ( dKWbtlnVzZKY + Fg0YJm19jg6un ) ; `Uh51CJZ1my: ms6BV1DMBx 
= ( dKWbtlnVzZKY + iBY1wF465kZc43I ) ; `uRAWNpvivV: ms6BV1DMBx = ( dKWbtlnVzZKY +
 iBY1wF465kZc43I ) ; `sFUXHxtwk9: ms6BV1DMBx = ( dKWbtlnVzZKY + iBY1wF465kZc43I )
 ; `ljF68bMmBk: ms6BV1DMBx = ( dKWbtlnVzZKY + iBY1wF465kZc43I ) ; `x2qOGrxolQc: ms6BV1DMBx 
= ( dKWbtlnVzZKY + iBY1wF465kZc43I ) ; `jtOKAfRTYK0: ms6BV1DMBx = ( dKWbtlnVzZKY 
+ iBY1wF465kZc43I ) ; `CFtFJ9DBPS: ms6BV1DMBx = ( rgW3lMDkqyNwd + fUFVvRbkqOOVT )
 ; `gn2exuAbLT: ms6BV1DMBx = ( rgW3lMDkqyNwd - fUFVvRbkqOOVT ) ; `pcePm8LD1v: ms6BV1DMBx 
= ( $signed(rgW3lMDkqyNwd) < $signed(fUFVvRbkqOOVT) ) ? 64'd1 : 64'd0 ; `TfvHDH83Ohj: 
ms6BV1DMBx = ( $unsigned(rgW3lMDkqyNwd) < $unsigned(fUFVvRbkqOOVT) ) ? 64'd1 : 64'd0 
; `ILodpBxBQ2: ms6BV1DMBx = ( rgW3lMDkqyNwd ^ fUFVvRbkqOOVT ) ; `u0w1PitU8: ms6BV1DMBx 
= ( rgW3lMDkqyNwd | fUFVvRbkqOOVT ) ; `KZhl8cmB7g: ms6BV1DMBx = ( rgW3lMDkqyNwd & 
fUFVvRbkqOOVT ) ; `Fr1hfyiDMp: ms6BV1DMBx = ( (rgW3lMDkqyNwd) << (fUFVvRbkqOOVT) 
) ; `WPJyCzckVm: ms6BV1DMBx = ( (rgW3lMDkqyNwd) >> (fUFVvRbkqOOVT) ) ; `vso7X0E69R: 
ms6BV1DMBx = ( $signed(rgW3lMDkqyNwd) >>> (fUFVvRbkqOOVT) ) ; `h5VOpvRi5m: ms6BV1DMBx 
= ( rgW3lMDkqyNwd * fUFVvRbkqOOVT ) ; `iaOgcOZ2T2: ms6BV1DMBx = 0 ; default: ms6BV1DMBx 
= 0 ; endcase end end always @(posedge qRw ) begin if ( QJ9hkSUr86g6 == `Gtp0AMLiF4c 
) begin $fwrite(32'h80000002, "\n\033[31mHALT-%1d\n\033[0m", rgW3lMDkqyNwd[31:0] 
); $finish; end if( QJ9hkSUr86g6 == `aQR5mt82cTnp ) begin $fwrite(32'h80000002, "%c", 
rgW3lMDkqyNwd[7:0] ); end end assign hq6Uv7FRgdqu = ( REAeHbGOa ) ? ( dKWbtlnVzZKY+
4 ) : ( Pfjw95PQlMmB ) ? ( {{(32){ms6BV1DMBx[31]}}, ms6BV1DMBx[31:0]} ) : ( ms6BV1DMBx 
) ; assign Ha5GWqunguUc = ( QJ9hkSUr86g6 == `Uh51CJZ1my ) ? ( rgW3lMDkqyNwd == fUFVvRbkqOOVT 
) : ( QJ9hkSUr86g6 == `uRAWNpvivV ) ? ( rgW3lMDkqyNwd != fUFVvRbkqOOVT ) : ( QJ9hkSUr86g6 
== `sFUXHxtwk9 ) ? ( $signed(rgW3lMDkqyNwd) < $signed(fUFVvRbkqOOVT) ) : ( QJ9hkSUr86g6 
== `ljF68bMmBk ) ? ( $signed(rgW3lMDkqyNwd) >= $signed(fUFVvRbkqOOVT) ) : ( QJ9hkSUr86g6 
== `x2qOGrxolQc) ? ( $unsigned(rgW3lMDkqyNwd) < $unsigned(fUFVvRbkqOOVT) ) : ( QJ9hkSUr86g6 
== `jtOKAfRTYK0) ? ( $unsigned(rgW3lMDkqyNwd) >= $unsigned(fUFVvRbkqOOVT) ) : 1'b0 
; assign OUTEggu4pjvZO = ( Ha5GWqunguUc || REAeHbGOa ) ; assign JDqNhAjgmujRj = (
 GFv == 1'b1 ) ? `hJZJwMU5 : ( REAeHbGOa ) ? ( ms6BV1DMBx ) : ( Ha5GWqunguUc ) ? 
( ms6BV1DMBx ) : `hJZJwMU5 ; endmodule 
