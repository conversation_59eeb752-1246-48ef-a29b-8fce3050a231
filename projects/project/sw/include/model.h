#ifndef __MODEL_H__
#define __MODEL_H__

#define ADDR_DATA           0x80800000
#define ADDR_SAVE           0x80f00000

#define INPUT_INT8_CONV1    3*32*32
#define WEIGHT_INT8_CONV1   12*3*5*5
#define SCALE_INT8_CONV1    1
#define WEIGHT_INT8_CONV2   32*12*3*3
#define SCALE_INT8_CONV2    1
#define WEIGHT_INT8_FC1     32*6*6*256
#define SCALE_INT8_FC1      1
#define WEIGHT_INT8_FC2     256*64
#define SCALE_INT8_FC2      1
#define WEIGHT_INT8_FC3     64*10
#define BIAS_INT16_FC3      10*2
#define SCALE_INT8_FC3      1

#define OUTPUT_INT8_CONV1   12*28*28
#define OUTPUT_INT8_POOL1   12*14*14
#define OUTPUT_INT8_CONV2   32*12*12
#define OUTPUT_INT8_POOL2   32*6*6
#define OUTPUT_INT8_FC1     256
#define OUTPUT_INT8_FC2     64
#define OUTPUT_INT8_FC3     10

#define ADDR_INPUT          ADDR_DATA
#define ADDR_WCONV1         ADDR_INPUT  + INPUT_INT8_CONV1
#define ADDR_SCONV1         ADDR_WCONV1 + WEIGHT_INT8_CONV1
#define ADDR_WCONV2         ADDR_SCONV1 + SCALE_INT8_CONV1
#define ADDR_SCONV2         ADDR_WCONV2 + WEIGHT_INT8_CONV2
#define ADDR_WFC1           ADDR_SCONV2 + SCALE_INT8_CONV2
#define ADDR_SFC1           ADDR_WFC1   + WEIGHT_INT8_FC1
#define ADDR_WFC2           ADDR_SFC1   + SCALE_INT8_FC1
#define ADDR_SFC2           ADDR_WFC2   + WEIGHT_INT8_FC2
#define ADDR_WFC3           ADDR_SFC2   + SCALE_INT8_FC2
#define ADDR_BFC3           ADDR_WFC3   + WEIGHT_INT8_FC3
#define ADDR_SFC3           ADDR_BFC3   + BIAS_INT16_FC3


#define ADDR_OUTCONV1       ADDR_SAVE   
#define ADDR_OUTPOOL1       ADDR_OUTCONV1 + OUTPUT_INT8_CONV1
#define ADDR_OUTCONV2       ADDR_OUTPOOL1 + OUTPUT_INT8_POOL1
#define ADDR_OUTPOOL2       ADDR_OUTCONV2 + OUTPUT_INT8_CONV2
#define ADDR_OUTFC1         ADDR_OUTPOOL2 + OUTPUT_INT8_POOL2
#define ADDR_OUTFC2         ADDR_OUTFC1   + OUTPUT_INT8_FC1
#define ADDR_OUTFC3         ADDR_OUTFC2   + OUTPUT_INT8_FC2

#endif 
