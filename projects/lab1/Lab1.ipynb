{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Lab 1 Numpy CNN for inference"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this lab, you are required to fill in the blanks with your code **independently** to complete the inference process of a CNN. Note that there's a bonus at the end of this lab.\n", "\n", "**Requirements**\n", "\n", "**1. Complete the codes independently.**\n", "\n", "**2. Make sure your results are reproducible.**\n", "\n", "**3. Do not change the structure of the CNN and the given codes.**\n", "\n", "**4. Do not add additional libraries.**\n", "\n", "**Submission**\n", "\n", "**1. Please submit only this ipynb file via Blackboard.**\n", "\n", "**2. Name the ipynb file as \"StudentID_StudentName\".**\n", "\n", "**3. Submit before Oct. 22nd 23:59:59.**"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Define a simple CNN that classifies CIFAR images.\n", "The network provided is similar to LeNet-5, and it has the following architecture:\n", "\n", "**Layer** | **Type** | **Input Shape** | **Output Shape** | **Activation**\n", "--- | --- | --- | --- | ---\n", "conv1 | Convolutional | 3x32x32 | 12x28x28 | ReLU \n", "pool1 | Max pool | 12x28x28 | 12x14x14 | None                \n", "conv2 | Convolutional | 12x14x14 | 32x12x12 | ReLU                \n", "pool2 | Max pool | 32x12x12 | 32x6x6 | None                \n", "fc1 | Fully-connected | 1152 | 256 | ReLU                \n", "fc2 | Fully-connected | 256 | 64 | ReLU                \n", "fc3 | Fully-connected | 64 | 10 | None                \n", "\n", "Next, we will build convolution, relu, max-pooling and fully-connected layers using **numpy** respectively (only forward propagation is required for inference)."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["class ReLU():\n", "    def __init__(self):\n", "        pass\n", "    def forward(self,input):\n", "        # TODO (5 pts) \n", "        # forward propagation for relu layer\n", "        output = np.maximum(0, input)\n", "        return output"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["class ConvLayer:  \n", "    def __init__(self, input_channels, output_channels, kernel_size, stride, padding):   \n", "        self.input_channels = input_channels   \n", "        self.kernel_size = kernel_size  \n", "        self.stride = stride\n", "        self.padding = padding \n", "  \n", "    def forward(self, x, weight):  \n", "        \"\"\"    \n", "        input x: (N, C, H, W) [batchsize, input channels, x_height, x_width]\n", "        input w: (K, C, R, S) [output channels, input channels, w_height, w_width] \n", "        output: (N, K, P, Q) [batchsize, output channels, output_height, output_width]\n", "        \"\"\"  \n", "        N, C, H, W = x.shape\n", "        K, C, R, S = weight.shape\n", "        \n", "        # TODO (5 pts)  \n", "        # complete padding operation\n", "        x_padded = np.pad(x, ((0, 0), (0, 0), (self.padding, self.padding), (self.padding, self.padding)), mode='constant', constant_values=0)\n", "        \n", "        # TODO (5 pts)  \n", "        # compute output size using self.padding and self.stride\n", "        P = (H + 2 * self.padding - R) // self.stride + 1\n", "        Q = (W + 2 * self.padding - S) // self.stride + 1\n", "        \n", "        output = np.zeros((N, K, P, Q)) \n", "        # TODO (20 pts)\n", "        # complete convolution operation\n", "        for n in range(N):  # for each sample in batch\n", "            for k in range(K):  # for each output channel\n", "                for p in range(P):  # for each output height position\n", "                    for q in range(Q):  # for each output width position\n", "                        # Extract the patch\n", "                        h_start = p * self.stride\n", "                        h_end = h_start + R\n", "                        w_start = q * self.stride\n", "                        w_end = w_start + S\n", "                        \n", "                        patch = x_padded[n, :, h_start:h_end, w_start:w_end]\n", "                        # Compute convolution for this position\n", "                        output[n, k, p, q] = np.sum(patch * weight[k, :, :, :])\n", "        \n", "        return output\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import numpy as np  \n", "  \n", "class MaxPooling2D:  \n", "    def __init__(self, pool_size=(2, 2), stride=2):  \n", "        self.pool_size = pool_size  \n", "        self.stride = stride  \n", "  \n", "    def forward(self, x):  \n", "        \"\"\"    \n", "        input x: (N, C, H, W) [batchsize, input channels, x_height, x_width]\n", "        output: (N, C, pooled_height, pooled_width)\n", "        \"\"\"  \n", "        N, C, H, W = x.shape  \n", "        # TODO (5 pts) \n", "        # compute output size using self.pool_size and self.stride\n", "        pooled_height = (H - self.pool_size[0]) // self.stride + 1\n", "        pooled_width = (W - self.pool_size[1]) // self.stride + 1\n", "  \n", "        output = np.zeros((N, C, pooled_height, pooled_width))  \n", "        # TODO (10 pts)\n", "        # complete max-pooling operation \n", "        for n in range(N):  # for each sample in batch\n", "            for c in range(C):  # for each channel\n", "                for ph in range(pooled_height):  # for each output height position\n", "                    for pw in range(pooled_width):  # for each output width position\n", "                        # Define the pooling window\n", "                        h_start = ph * self.stride\n", "                        h_end = h_start + self.pool_size[0]\n", "                        w_start = pw * self.stride\n", "                        w_end = w_start + self.pool_size[1]\n", "                        \n", "                        # Extract the pooling window and find maximum\n", "                        pool_region = x[n, c, h_start:h_end, w_start:w_end]\n", "                        output[n, c, ph, pw] = np.max(pool_region)\n", "  \n", "        return output"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "class fclayer():\n", "    def __init__(self, in_features, out_features):\n", "        self.in_features = in_features\n", "        self.out_features = out_features\n", "        \n", "    def forward(self, x, weight):   \n", "        # TODO (10 pts)\n", "        # complete forward propagation of fully-connected layer\n", "        # x shape: (batch_size, in_features)\n", "        # weight shape: (out_features, in_features)\n", "        # output shape: (batch_size, out_features)\n", "        output = np.dot(x, weight.T)  # Matrix multiplication\n", "        return output"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import torch\n", "\n", "# load trained parameters\n", "ckpt = torch.load('../lab0/model_weights.pth')"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def lenet_inf(x,ckpt):\n", "    # TODO (20 pts)\n", "    # build the CNN network using classes above\n", "    \n", "    # Initialize layers\n", "    relu = ReLU()\n", "    conv1 = ConvLayer(input_channels=3, output_channels=12, kernel_size=5, stride=1, padding=0)\n", "    pool1 = MaxPooling2D(pool_size=(2, 2), stride=2)\n", "    conv2 = ConvLayer(input_channels=12, output_channels=32, kernel_size=3, stride=1, padding=0)\n", "    pool2 = MaxPooling2D(pool_size=(2, 2), stride=2)\n", "    fc1 = fclayer(in_features=1152, out_features=256)\n", "    fc2 = fclayer(in_features=256, out_features=64)\n", "    fc3 = fclayer(in_features=64, out_features=10)\n", "    \n", "    # Extract weights from checkpoint\n", "    conv1_weight = ckpt['conv1.weight'].numpy()\n", "    conv2_weight = ckpt['conv2.weight'].numpy()\n", "    fc1_weight = ckpt['fc1.weight'].numpy()\n", "    fc2_weight = ckpt['fc2.weight'].numpy()\n", "    fc3_weight = ckpt['fc3.weight'].numpy()\n", "    \n", "    # Forward propagation\n", "    # Conv1 + ReLU: 3x32x32 -> 12x28x28\n", "    out = conv1.forward(x, conv1_weight)\n", "    out = relu.forward(out)\n", "    \n", "    # Pool1: 12x28x28 -> 12x14x14\n", "    out = pool1.forward(out)\n", "    \n", "    # Conv2 + ReLU: 12x14x14 -> 32x12x12\n", "    out = conv2.forward(out, conv2_weight)\n", "    out = relu.forward(out)\n", "    \n", "    # Pool2: 32x12x12 -> 32x6x6\n", "    out = pool2.forward(out)\n", "    \n", "    # Flatten: 32x6x6 -> 1152\n", "    out = out.reshape(out.shape[0], -1)\n", "    \n", "    # FC1 + ReLU: 1152 -> 256\n", "    out = fc1.forward(out, fc1_weight)\n", "    out = relu.forward(out)\n", "    \n", "    # FC2 + ReLU: 256 -> 64\n", "    out = fc2.forward(out, fc2_weight)\n", "    out = relu.forward(out)\n", "    \n", "    # FC3: 64 -> 10\n", "    output = fc3.forward(out, fc3_weight)\n", "    \n", "    return output\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import imageio.v2 as imageio\n", "from skimage.transform import resize\n", "\n", "# In this lab we will only infer 1 picture from CIFAR-10 datasets to save running time\n", "# you can try different pictures in ./pictures\n", "input_image = imageio.imread('./pictures/bird1.png')\n", "\n", "# TODO (5 pts)\n", "# normalize the pixel into [0,1]\n", "image = input_image.astype(np.float32) / 255.0\n", "\n", "# TODO (5 pts)\n", "# alter the size of the pixel matrix from (32,32,3) to (1,3,32,32) to fit convolution layer\n", "image = np.transpose(image, (2, 0, 1))  # Change from (H, W, C) to (C, H, W)\n", "image = np.expand_dims(image, axis=0)   # Add batch dimension: (1, C, H, W)\n", "\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'conv1.weight'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                  <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[9]\u001b[39m\u001b[32m, line 4\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# TODO (5 pts)\u001b[39;00m\n\u001b[32m      2\u001b[39m \u001b[38;5;66;03m# inference using lenet_inf created above\u001b[39;00m\n\u001b[32m      3\u001b[39m \u001b[38;5;66;03m# note that the inference process of 1 picture using numpy may take more than 20 minutes\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m4\u001b[39m output = \u001b[43mlenet_inf\u001b[49m\u001b[43m(\u001b[49m\u001b[43mimage\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mckpt\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m      5\u001b[39m label = np.argmax(output)\n\u001b[32m      7\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mmatplotlib\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpyplot\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mplt\u001b[39;00m\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[7]\u001b[39m\u001b[32m, line 16\u001b[39m, in \u001b[36mlenet_inf\u001b[39m\u001b[34m(x, ckpt)\u001b[39m\n\u001b[32m     13\u001b[39m fc3 = fclayer(in_features=\u001b[32m64\u001b[39m, out_features=\u001b[32m10\u001b[39m)\n\u001b[32m     15\u001b[39m \u001b[38;5;66;03m# Extract weights from checkpoint\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m16\u001b[39m conv1_weight = \u001b[43mckpt\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mconv1.weight\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m]\u001b[49m.numpy()\n\u001b[32m     17\u001b[39m conv2_weight = ckpt[\u001b[33m'\u001b[39m\u001b[33mconv2.weight\u001b[39m\u001b[33m'\u001b[39m].numpy()\n\u001b[32m     18\u001b[39m fc1_weight = ckpt[\u001b[33m'\u001b[39m\u001b[33mfc1.weight\u001b[39m\u001b[33m'\u001b[39m].numpy()\n", "\u001b[31m<PERSON><PERSON><PERSON><PERSON>r\u001b[39m: 'conv1.weight'"]}], "source": ["# TODO (5 pts)\n", "# inference using lenet_inf created above\n", "# note that the inference process of 1 picture using numpy may take more than 20 minutes\n", "output = lenet_inf(image, ckpt)\n", "label = np.argmax(output)\n", "\n", "import matplotlib.pyplot as plt\n", "# visualize the picture to be classified\n", "plt.imshow(input_image)\n", "print(\"Predicted label:\",label)\n", "print(\"Ground truth: 3\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Bonus: Calculate the number of computations and parameters. Visualize your results directly in the outputs of your codes. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["Give your answer here.  "]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.6"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}