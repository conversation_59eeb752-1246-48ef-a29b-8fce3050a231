import torch
import torchvision
import torchvision.transforms as transforms
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import os 
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

if torch.cuda.is_available():
    device = torch.device('cuda')
else:
    device = torch.device('cpu')
torch.manual_seed(2024219)

transform = transforms.Compose(
    [transforms.ToTensor(),
     transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5))])

trainset = torchvision.datasets.CIFAR10(root='../data', train=True,
                                        download=True, transform=transform)
trainloader = torch.utils.data.DataLoader(trainset, batch_size=100,
                                          shuffle=False, num_workers=8)

testset = torchvision.datasets.CIFAR10(root='../data', train=False,
                                       download=True, transform=transform)
testloader = torch.utils.data.DataLoader(testset, batch_size=100,
                                         shuffle=False, num_workers=8)

class Net(nn.Module):
    def __init__(self):
        super(Net, self).__init__()
        self.conv1 = nn.Conv2d(3, 12, 5, bias=False)
        self.pool = nn.MaxPool2d(2, 2) # run after each conv (hence the 5x5 FC layer)
        self.conv2 = nn.Conv2d(12, 32, 3, bias=False)
        self.fc1 = nn.Linear(32 * 6 * 6, 256, bias=False)
        self.fc2 = nn.Linear(256, 64, bias=False)
        self.fc3 = nn.Linear(64, 10, bias=False)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = self.pool(F.relu(self.conv1(x)))
        x = self.pool(F.relu(self.conv2(x)))
        x = x.view(-1, 32 * 6 * 6)
        x = F.relu(self.fc1(x))
        x = F.relu(self.fc2(x))
        x = self.fc3(x)
        return x

net = Net().to(device)

from torch.utils.data import DataLoader

def train(model: nn.Module, dataloader: DataLoader):
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.SGD(model.parameters(), lr=0.015, momentum=0.9)

    for epoch in range(10):  # loop over the dataset multiple (2) times

        running_loss = 0.0
        for i, data in enumerate(dataloader, 0):
            # get the inputs; data is a list of [inputs, labels]
            inputs, labels = data

            inputs = inputs.to(device)
            labels = labels.to(device)

            # zero the parameter gradients
            optimizer.zero_grad()

            # forward + backward + optimize
            outputs = model(inputs)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()

            # print statistics
            running_loss += loss.item()
            if i % 100 == 99:    # print every 100 mini-batches
                print('[Epoch: %d, Iteration: %5d] loss: %.3f' %
                    (epoch + 1, i + 1, running_loss / 100))
                running_loss = 0.0

    print('Finished Training')

def test(model: nn.Module, dataloader: DataLoader, max_samples=None) -> float:
    correct = 0
    total = 0
    n_inferences = 0

    with torch.no_grad():
        for data in dataloader:
            images, labels = data

            images = images.to(device)
            labels = labels.to(device)

            outputs = model(images) # get 1 batch worth of image predictions (i.e. 4 predictions of 10 each)
            other, predicted = torch.max(outputs.data, 1) # other == values, predicted == indicies
            total += labels.size(0)
            correct += (predicted == labels).sum().item()

            if max_samples:
                n_inferences += images.shape[0]
                if n_inferences > max_samples:
                    break
    
    return 100 * correct / total

train(net, trainloader)

score = test(net, testloader)
print('Accuracy of the network on the test images: {}%'.format(score))

from copy import deepcopy

def copy_model(model: nn.Module) -> nn.Module:
    result = deepcopy(model)

    # Copy over the extra metadata we've collected which copy.deepcopy doesn't capture
    if hasattr(model, 'input_activations'):
        result.input_activations = deepcopy(model.input_activations)

    for result_layer, original_layer in zip(result.children(), model.children()):
        if isinstance(result_layer, nn.Conv2d) or isinstance(result_layer, nn.Linear):
            if hasattr(original_layer.weight, 'scale'):
                result_layer.weight.scale = deepcopy(original_layer.weight.scale)
            if hasattr(original_layer, 'activations'):
                result_layer.activations = deepcopy(original_layer.activations)
            if hasattr(original_layer, 'output_scale'):
                result_layer.output_scale = deepcopy(original_layer.output_scale)

    return result

# Possible required lib
import matplotlib.pyplot as plt
import numpy as np

# ADD YOUR CODE HERE

# You can get a flattened vector of the weights of fc1 like this:
#   fc1_weights = net.fc1.weight.data.cpu().view(-1)
# Try plotting a histogram of fc1_weights (and the weights of all the other layers as well)
# You can use "hist" from the matplotlib.pyplot package.
# Extract layer weights
layers = [
    ('conv1', net.conv1.weight.data.cpu().view(-1)),
    ('conv2', net.conv2.weight.data.cpu().view(-1)),
    ('fc1', net.fc1.weight.data.cpu().view(-1)),
    ('fc2', net.fc2.weight.data.cpu().view(-1)),
    ('fc3', net.fc3.weight.data.cpu().view(-1))
]

# Create a figure with subplots
fig, axs = plt.subplots(len(layers), 1, figsize=(12, 15))
fig.suptitle('Weight Distributions of CNN Layers', fontsize=16)

# Plot histograms for each layer
for i, (layer_name, weights) in enumerate(layers):
    axs[i].hist(weights.numpy(), bins=50)
    axs[i].set_title(f'Weight Distribution of {layer_name}')
    axs[i].set_xlabel('Weight Value')
    axs[i].set_ylabel('Frequency')
    
    # Calculate statistics
    min_val = weights.min().item()
    max_val = weights.max().item()
    mean_val = weights.mean().item()
    std_val = weights.std().item()
    
    # Calculate 3-sigma range
    sigma_min = mean_val - 3 * std_val
    sigma_max = mean_val + 3 * std_val
    
    # Add vertical lines for min, max, and 3-sigma bounds
    axs[i].axvline(min_val, color='r', linestyle='--', label=f'Min: {min_val:.4f}')
    axs[i].axvline(max_val, color='r', linestyle='--', label=f'Max: {max_val:.4f}')
    axs[i].axvline(sigma_min, color='g', linestyle='--', label=f'μ-3σ: {sigma_min:.4f}')
    axs[i].axvline(sigma_max, color='g', linestyle='--', label=f'μ+3σ: {sigma_max:.4f}')
    axs[i].axvline(mean_val, color='k', linestyle='-', label=f'Mean: {mean_val:.4f}')
    
    # Add a text box with statistics
    stats_text = f'Min: {min_val:.4f}\nMax: {max_val:.4f}\nMean: {mean_val:.4f}\nStd: {std_val:.4f}\n'
    stats_text += f'Range: {max_val - min_val:.4f}\n3-Sigma Range: {sigma_max - sigma_min:.4f}'
    axs[i].text(0.02, 0.95, stats_text, transform=axs[i].transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))

plt.tight_layout()
plt.subplots_adjust(top=0.95)
plt.show()

# Print a summary table of the ranges
print("Layer\tActual Range\t3-Sigma Range\tComparison")
print("-----\t------------\t-------------\t----------")
for layer_name, weights in layers:
    min_val = weights.min().item()
    max_val = weights.max().item()
    mean_val = weights.mean().item()
    std_val = weights.std().item()
    
    actual_range = max_val - min_val
    sigma_range = 6 * std_val  # μ+3σ - (μ-3σ) = 6σ
    
    comparison = "3-sigma < actual" if sigma_range < actual_range else "3-sigma > actual"
    
    print(f"{layer_name}\t{actual_range:.6f}\t{sigma_range:.6f}\t{comparison}")

# Useful functions: torch.min/max/mean/std

net_q2 = copy_model(net)

from typing import Tuple

def quantized_weights(weights: torch.Tensor) -> Tuple[torch.Tensor, float]:
      '''
      Quantize the weights so that all values are integers between -128 and 127.
      You may want to use the maxmimum absolute value of total range, 3-sigma range, 
      or other ranges for symmetric quantization when deciding just what factors to 
      scale the float32 values by.

      Parameters:
      weights (Tensor): The unquantized weights

      Returns:
      (Tensor, float): A tuple with the following elements:
                * The weights in quantized form, where every value is an integer between -128 and 127.
                  The "dtype" will still be "float", but the values themselves should all be integers.
                * The scaling factor that your weights were multiplied by.
                  This value does not need to be an 8-bit integer.
      '''

      ''' 
      ADD YOUR CODE HERE to compute 'result' and change 'scale'. 
      We consider only symmetric quantization, which means zeros point is exactly 0.
      After that, return the 'scale' together with the quantized number, 'result', in [-128,127].
      '''
      # Find the maximum absolute value in the weights
      max_abs_val = torch.max(torch.abs(weights))
      
      # Calculate the scale factor (127 is the maximum value for 8-bit signed integer)
      scale = max_abs_val / 127.0
      
      # Scale the weights and round to nearest integer
      result = torch.round(weights / scale)
      
      return torch.clamp(result, min=-128, max=127), scale


def quantize_layer_weights(model: nn.Module):
    # Quantize the weights layer by layer and record the scale factors and quantized weights
    for layer in model.children():
        if isinstance(layer, nn.Conv2d) or isinstance(layer, nn.Linear):
            # Quantize the weights using the function you just developed
            q_layer_data, scale = quantized_weights(layer.weight.data)
            q_layer_data = q_layer_data.to(device)

            layer.weight.data = q_layer_data
            layer.weight.scale = scale
            
            # Check if the weights are quantized properly, your code should be okay if no exception is raised.
            # Otherwise, please check your code.
            if (q_layer_data < -128).any() or (q_layer_data > 127).any():
                raise Exception("Quantized weights of {} layer include values out of bounds for an 8-bit signed integer".format(layer.__class__.__name__))
            if (q_layer_data != q_layer_data.round()).any():
                raise Exception("Quantized weights of {} layer include non-integer values".format(layer.__class__.__name__))

quantize_layer_weights(net_q2)

score = test(net_q2, testloader)
print('Accuracy of the network after quantizing all weights: {}%'.format(score))

def register_activation_profiling_hooks(model: Net):
    # initialize the activations with empty ndarray
    model.input_activations = np.empty(0)
    model.conv1.activations = np.empty(0)
    model.conv2.activations = np.empty(0)
    model.fc1.activations = np.empty(0)
    model.fc2.activations = np.empty(0)
    model.fc3.activations = np.empty(0)

    model.profile_activations = True

    def conv1_activations_hook(layer, x, y):
        if model.profile_activations:
            model.input_activations = np.append(model.input_activations, x[0].cpu().view(-1))
    model.conv1.register_forward_hook(conv1_activations_hook)
    # function register_forward_hook is used to obtain the activations, which would otherwise be abandoned as
    # intermediate variables. For more details, please refer to 
    # https://pytorch.org/docs/stable/notes/modules.html#module-initialization
    # Module Hooks

    def conv2_activations_hook(layer, x, y):
        if model.profile_activations:
            model.conv1.activations = np.append(model.conv1.activations, x[0].cpu().view(-1))
    model.conv2.register_forward_hook(conv2_activations_hook)

    def fc1_activations_hook(layer, x, y):
        if model.profile_activations:
            model.conv2.activations = np.append(model.conv2.activations, x[0].cpu().view(-1))
    model.fc1.register_forward_hook(fc1_activations_hook)

    def fc2_activations_hook(layer, x, y):
        if model.profile_activations:
            model.fc1.activations = np.append(model.fc1.activations, x[0].cpu().view(-1))
    model.fc2.register_forward_hook(fc2_activations_hook)

    def fc3_activations_hook(layer, x, y):
        if model.profile_activations:
            model.fc2.activations = np.append(model.fc2.activations, x[0].cpu().view(-1))
            model.fc3.activations = np.append(model.fc3.activations, y[0].cpu().view(-1))
    model.fc3.register_forward_hook(fc3_activations_hook)


net_q3 = copy_model(net)
# activate the hook record activations
register_activation_profiling_hooks(net_q3)

# Run through the training dataset again while profiling the input and output activations this time
# We don't actually have to perform gradient descent for this, so we can use the "test" function
test(net_q3, trainloader, max_samples=1000)
net_q3.profile_activations = False

# Fetch the activations
input_activations = net_q3.input_activations
conv1_output_activations = net_q3.conv1.activations
conv2_output_activations = net_q3.conv2.activations
fc1_output_activations = net_q3.fc1.activations
fc2_output_activations = net_q3.fc2.activations
fc3_output_activations = net_q3.fc3.activations

# ADD YOUR CODE HERE to plot distributions of activations
# Plot histograms of the following variables:
#   input_activations
#   conv1_output_activations
#   conv2_output_activations
#   fc1_output_activations
#   fc2_output_activations
#   fc3_output_activation
# Create a figure with subplots for each activation
activations = [
    ('Input', input_activations),
    ('Conv1 Output', conv1_output_activations),
    ('Conv2 Output', conv2_output_activations),
    ('FC1 Output', fc1_output_activations),
    ('FC2 Output', fc2_output_activations),
    ('FC3 Output', fc3_output_activations)
]

fig, axs = plt.subplots(len(activations), 1, figsize=(12, 18))
fig.suptitle('Activation Distributions', fontsize=16)

# Plot histograms for each activation
for i, (name, activation_values) in enumerate(activations):
    axs[i].hist(activation_values, bins=50)
    axs[i].set_title(f'{name} Distribution')
    axs[i].set_xlabel('Activation Value')
    axs[i].set_ylabel('Frequency')
    
    # Calculate statistics
    min_val = np.min(activation_values)
    max_val = np.max(activation_values)
    mean_val = np.mean(activation_values)
    std_val = np.std(activation_values)
    
    # Calculate 3-sigma range
    sigma_min = mean_val - 3 * std_val
    sigma_max = mean_val + 3 * std_val
    
    # Add vertical lines for min, max, and 3-sigma bounds
    axs[i].axvline(min_val, color='r', linestyle='--', label=f'Min: {min_val:.4f}')
    axs[i].axvline(max_val, color='r', linestyle='--', label=f'Max: {max_val:.4f}')
    axs[i].axvline(sigma_min, color='g', linestyle='--', label=f'μ-3σ: {sigma_min:.4f}')
    axs[i].axvline(sigma_max, color='g', linestyle='--', label=f'μ+3σ: {sigma_max:.4f}')
    axs[i].axvline(mean_val, color='k', linestyle='-', label=f'Mean: {mean_val:.4f}')
    
    # Add a text box with statistics
    actual_range = max_val - min_val
    sigma_range = sigma_max - sigma_min
    stats_text = f'Min: {min_val:.4f}\nMax: {max_val:.4f}\nMean: {mean_val:.4f}\nStd: {std_val:.4f}\n'
    stats_text += f'Range: {actual_range:.4f}\n3-Sigma Range: {sigma_range:.4f}'
    axs[i].text(0.02, 0.95, stats_text, transform=axs[i].transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))

plt.tight_layout()
plt.subplots_adjust(top=0.95)
plt.show()

# Print a summary table of the ranges
print("Activation\tActual Range\t3-Sigma Range\tComparison")
print("---------\t------------\t-------------\t----------")
for name, activation_values in activations:
    min_val = np.min(activation_values)
    max_val = np.max(activation_values)
    mean_val = np.mean(activation_values)
    std_val = np.std(activation_values)
    
    actual_range = max_val - min_val
    sigma_range = 6 * std_val  # μ+3σ - (μ-3σ) = 6σ
    
    comparison = "3-sigma < actual" if sigma_range < actual_range else "3-sigma > actual"
    
    print(f"{name}\t{actual_range:.6f}\t{sigma_range:.6f}\t{comparison}")

# ADD YOUR CODE HERE to record the range and 3-sigma range of the activations

from typing import List

class NetQuantized(nn.Module):
    def __init__(self, net_with_weights_quantized: nn.Module):
        super(NetQuantized, self).__init__()
        
        net_init = copy_model(net_with_weights_quantized)

        self.conv1 = net_init.conv1
        self.pool = net_init.pool
        self.conv2 = net_init.conv2
        self.fc1 = net_init.fc1
        self.fc2 = net_init.fc2
        self.fc3 = net_init.fc3

        for layer in self.conv1, self.conv2, self.fc1, self.fc2, self.fc3:
            def pre_hook(l, x):
                x = x[0]
                if (x < -128).any() or (x > 127).any():
                    raise Exception("Input to {} layer is out of bounds for an 8-bit signed integer".format(l.__class__.__name__))
                if (x != x.round()).any():
                    raise Exception("Input to {} layer has non-integer values".format(l.__class__.__name__))

            layer.register_forward_pre_hook(pre_hook)

        # Calculate the scaling factor for the initial input to the CNN
        self.input_activations = net_with_weights_quantized.input_activations
        self.input_scale = NetQuantized.quantize_initial_input(self.input_activations)

        # Calculate the output scaling factors for all the layers of the CNN
        preceding_layer_scales = []
        for layer in self.conv1, self.conv2, self.fc1, self.fc2, self.fc3:
            layer.output_scale = NetQuantized.quantize_activations(layer.activations, layer.weight.scale, self.input_scale, preceding_layer_scales)
            preceding_layer_scales.append((layer.weight.scale, layer.output_scale))

    @staticmethod
    def quantize_initial_input(pixels: np.ndarray) -> float:
        '''
        Calculate a scaling factor for the images that are input to the first layer of the CNN.
        Remember to use symmetric scaling (zero-point = 0)
        Parameters:
        pixels (ndarray): The values of all the pixels which were part of the input image during training

        Returns:
        float: A scaling factor that the input should be multiplied by before being fed into the first layer.
               This value does not need to be an 8-bit integer.
        '''

        
        max_abs_val = np.max(np.abs(pixels))
        
        # Calculate the scale factor to map to [-127, 127] range
        # We use 127 instead of 128 to maintain symmetry around 0
        scale = 127.0 / max_abs_val if max_abs_val > 0 else 1.0
        
        return scale

    @staticmethod
    def quantize_activations(activations: np.ndarray, s_w: float, s_initial_input: float, ss: List[Tuple[float, float]]) -> float:
        '''
        Calculate a scaling factor to multiply the output of a layer by.
        Remember to use symmetric scaling (zero-point = 0)
        Parameters:
        activations (ndarray): The values of all the pixels which have been output by this layer during training
        s_w (float): The scale by which the weights of this layer were multiplied as part of the "quantize_weights" function you wrote earlier
        s_initial_input (float): The scale by which the initial input to the neural network was multiplied
        ss ([(float, float)]): A list of tuples, where each tuple represents the "weight scale" and "output scale" (in that order) for every preceding layer

        Returns:
        float: A scaling factor that the layer output should be multiplied by before being fed into the first layer.
               This value does not need to be an 8-bit integer.
        '''
        
        # Calculate the cumulative scale from all preceding layers
        cumulative_scale = s_initial_input
        for s_w_prev, s_out_prev in ss:
            cumulative_scale *= s_w_prev / s_out_prev
        
        # Apply current layer's weight scale
        cumulative_scale *= s_w
        

        activations_np = activations
        
        # Scale the activations to see their effective range
        scaled_activations = activations_np / cumulative_scale
        
        # Find the maximum absolute value in the scaled activations
        max_abs_val = np.max(np.abs(scaled_activations))
        
        # Calculate the output scale factor
        scale = max_abs_val / 127.0 if max_abs_val > 0 else 1.0
        
        return scale

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # You can access the output activation scales like this:
        #   fc1_output_scale = self.fc1.output_scale

        # To make sure that the outputs of each layer are integers between -128 and 127, you may need to use the following functions:
        #   * torch.round
        #   * torch.clamp

        # Scale the input by the input scale factor
        x = x * self.input_scale
        x = torch.clamp(torch.round(x), -128, 127)
        
        # Conv1 layer
        x = self.conv1(x)
        x = x * self.conv1.output_scale
        x = torch.clamp(torch.round(x), -128, 127)
        x = F.relu(x)
        x = self.pool(x)
        
        # Conv2 layer
        x = self.conv2(x)
        x = x * self.conv2.output_scale
        x = torch.clamp(torch.round(x), -128, 127)
        x = F.relu(x)
        x = self.pool(x)
        
        # Flatten
        x = x.view(-1, 32 * 6 * 6)
        
        # FC1 layer
        x = self.fc1(x)
        x = x * self.fc1.output_scale
        x = torch.clamp(torch.round(x), -128, 127)
        x = F.relu(x)
        
        # FC2 layer
        x = self.fc2(x)
        x = x * self.fc2.output_scale
        x = torch.clamp(torch.round(x), -128, 127)
        x = F.relu(x)
        
        # FC3 layer (final output)
        x = self.fc3(x)
        x = x * self.fc3.output_scale
        x = torch.clamp(torch.round(x), -128, 127)
        
        return x

# Merge the information from net_q2 and net_q3 together
net_init = copy_model(net_q2)  
net_init.input_activations = deepcopy(net_q3.input_activations) 
for layer_init, layer_q3 in zip(net_init.children(), net_q3.children()):
    if isinstance(layer_init, nn.Conv2d) or isinstance(layer_init, nn.Linear):
        layer_init.activations = deepcopy(layer_q3.activations)

net_quantized = NetQuantized(net_init)

score = test(net_quantized, testloader)
print('Accuracy of the network after quantizing both weights and activations: {}%'.format(score))

class NetWithBias(nn.Module):
    def __init__(self):
        super(NetWithBias, self).__init__()

        self.conv1 = nn.Conv2d(3, 12, 5, bias=False)
        self.pool = nn.MaxPool2d(2, 2)
        self.conv2 = nn.Conv2d(12, 32, 3, bias=False)
        self.fc1 = nn.Linear(32 * 6 * 6, 256, bias=False)
        self.fc2 = nn.Linear(256, 64, bias=False)
        self.fc3 = nn.Linear(64, 10, bias=True)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = self.pool(F.relu(self.conv1(x)))
        x = self.pool(F.relu(self.conv2(x)))
        x = x.view(-1, 32 * 6 * 6)
        x = F.relu(self.fc1(x))
        x = F.relu(self.fc2(x))
        x = self.fc3(x)
        return x

net_with_bias = NetWithBias().to(device)

train(net_with_bias, trainloader)

score = test(net_with_bias, testloader)
print('Accuracy of the network (with a bias) on the test images: {}%'.format(score))

register_activation_profiling_hooks(net_with_bias)
test(net_with_bias, trainloader, max_samples=400)
net_with_bias.profile_activations = False

net_with_bias_with_quantized_weights = copy_model(net_with_bias)
quantize_layer_weights(net_with_bias_with_quantized_weights)

score = test(net_with_bias_with_quantized_weights, testloader)
print('Accuracy of the network on the test images after all the weights are quantized but the bias isn\'t: {}%'.format(score))

# slightly clearer bias bounds (32b signed integer)
MIN_32B_SINT = -(2**31)
MAX_32B_SINT = (2**31) - 1

class NetQuantizedWithBias(NetQuantized): # get quantized weights & activations
    def __init__(self, net_with_weights_quantized: nn.Module):
        super(NetQuantizedWithBias, self).__init__(net_with_weights_quantized)
        
        preceding_scales = [(layer.weight.scale, layer.output_scale) for layer in self.children() if isinstance(layer, nn.Conv2d) or isinstance(layer, nn.Linear)][:-1]

        #print(self.fc3.bias.data)
        self.fc3.bias.data = NetQuantizedWithBias.quantized_bias(
            self.fc3.bias.data,
            self.fc3.weight.scale,
            self.input_scale,
            preceding_scales
        )
        #print(self.fc3.bias.data)

        if (self.fc3.bias.data < MIN_32B_SINT).any() or (self.fc3.bias.data > MAX_32B_SINT).any():
               raise Exception("Bias has values which are out of bounds for an 32-bit signed integer")
        if (self.fc3.bias.data != self.fc3.bias.data.round()).any():
             raise Exception("Bias has non-integer values")

    @staticmethod
    def quantized_bias(bias: torch.Tensor, s_w: float, s_initial_input: float, ss: List[Tuple[float, float]]) -> torch.Tensor:
        '''
        Quantize the bias so that all values are integers between MIN_32B_SINT and MAX_32B_SINT.

        Parameters:
        bias (Tensor): The floating point values of the bias
        s_w (float): The scale by which the weights of this layer were multiplied
        s_initial_input (float): The scale by which the initial input to the neural network was multiplied
        ss ([(float, float)]): A list of tuples, where each tuple represents the "weight scale" and "output scale" (in that order) for every preceding layer

        Returns:
        Tensor: The bias in quantized form, where every value is an integer between MIN_32B_SINT and MAX_32B_SINT.
                The "dtype" will still be "float", but the values themselves should all be integers.
        '''

        # Calculate the cumulative scale from all preceding layers
        cumulative_scale = s_initial_input
        for s_w_prev, s_out_prev in ss:
            cumulative_scale *= s_w_prev / s_out_prev
        
        # Apply current layer's weight scale
        cumulative_scale *= s_w
        
        # The bias scale should match the scale of the layer's output before applying s_out
        # This ensures the bias is in the same scale as W*In
        scale_bias = cumulative_scale

        return torch.clamp((bias * scale_bias).round(), min=MIN_32B_SINT, max=MAX_32B_SINT)


net_quantized_with_bias = NetQuantizedWithBias(net_with_bias_with_quantized_weights)

score = test(net_quantized_with_bias, testloader)
print('Accuracy of the network on the test images after all the weights and the bias are quantized: {}%'.format(score))