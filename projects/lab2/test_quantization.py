#!/usr/bin/env python3

import numpy as np
import torch
import torch.nn as nn
from typing import List, <PERSON><PERSON>

def quantize_initial_input(pixels) -> float:
    '''
    Calculate a scaling factor for the images that are input to the first layer of the CNN.
    Remember to use symmetric scaling (zero-point = 0)
    '''
    # Find the maximum absolute value in the input pixels
    # Convert to numpy if it's a tensor
    if hasattr(pixels, 'detach'):
        pixels_np = pixels.detach().cpu().numpy()
    else:
        pixels_np = pixels
    
    max_abs_val = np.max(np.abs(pixels_np))
    
    # Calculate the scale factor to map to [-127, 127] range
    # We use 127 instead of 128 to maintain symmetry around 0
    scale = 127.0 / max_abs_val if max_abs_val > 0 else 1.0
    
    return scale

def quantize_activations(activations, s_w: float, s_initial_input: float, ss: List[Tuple[float, float]]) -> float:
    '''
    Calculate a scaling factor to multiply the output of a layer by.
    Remember to use symmetric scaling (zero-point = 0)
    '''
    # Calculate the cumulative scale from all preceding layers
    cumulative_scale = s_initial_input
    for s_w_prev, s_out_prev in ss:
        cumulative_scale *= s_w_prev / s_out_prev
    
    # Apply current layer's weight scale
    cumulative_scale *= s_w
    
    # Convert to numpy if it's a tensor
    if hasattr(activations, 'detach'):
        activations_np = activations.detach().cpu().numpy()
    else:
        activations_np = activations
    
    # Scale the activations to see their effective range
    scaled_activations = activations_np / cumulative_scale
    
    # Find the maximum absolute value in the scaled activations
    max_abs_val = np.max(np.abs(scaled_activations))
    
    # Calculate the output scale factor
    scale = max_abs_val / 127.0 if max_abs_val > 0 else 1.0
    
    return scale

# Test the functions
if __name__ == "__main__":
    print("Testing quantization functions...")
    
    # Test quantize_initial_input
    test_pixels = np.random.randn(100, 3, 32, 32) * 2.0  # Random pixels with range roughly [-6, 6]
    input_scale = quantize_initial_input(test_pixels)
    print(f"Input scale: {input_scale}")
    print(f"Max abs value in pixels: {np.max(np.abs(test_pixels))}")
    print(f"Scaled max value: {np.max(np.abs(test_pixels)) * input_scale}")
    
    # Test quantize_activations
    test_activations = np.random.randn(100, 256) * 5.0  # Random activations
    s_w = 0.1
    s_initial_input = input_scale
    ss = [(0.05, 0.2), (0.08, 0.15)]  # Example preceding layer scales
    
    output_scale = quantize_activations(test_activations, s_w, s_initial_input, ss)
    print(f"Output scale: {output_scale}")
    
    # Test with PyTorch tensors
    test_tensor = torch.randn(10, 64) * 3.0
    tensor_scale = quantize_activations(test_tensor, s_w, s_initial_input, ss)
    print(f"Tensor output scale: {tensor_scale}")
    
    print("All tests passed!")
